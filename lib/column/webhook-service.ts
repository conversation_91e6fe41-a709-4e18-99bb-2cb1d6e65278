import crypto from 'crypto';
import { columnClient } from './column-client';
import {
    Webhook,
    WebhookDelivery,
    ColumnEvent,
    ColumnListResponse
} from '@/types/column.types';

export class ColumnWebhookService {
    private webhookSecret: string;

    constructor() {
        this.webhookSecret = process.env.COLUMN_WEBHOOK_SECRET || '';
    }

    // ============================================================================
    // WEBHOOK MANAGEMENT
    // ============================================================================

    /**
     * Create a webhook endpoint
     */
    async createWebhook(data: {
        url: string;
        events: string[];
        description?: string;
    }): Promise<Webhook> {
        return await columnClient().post('/webhooks', data);
    }

    /**
     * Get a webhook by ID
     */
    async getWebhook(webhookId: string): Promise<Webhook> {
        return await columnClient().get(`/webhooks/${webhookId}`);
    }

    /**
     * List all webhooks
     */
    async listWebhooks(params?: {
        limit?: number;
        cursor?: string;
    }): Promise<ColumnListResponse<Webhook>> {
        return await columnClient().get('/webhooks', { params });
    }

    /**
     * Update a webhook
     */
    async updateWebhook(webhookId: string, data: {
        url?: string;
        events?: string[];
        status?: 'active' | 'inactive';
        description?: string;
    }): Promise<Webhook> {
        return await columnClient().put(`/webhooks/${webhookId}`, data);
    }

    /**
     * Delete a webhook
     */
    async deleteWebhook(webhookId: string): Promise<void> {
        await columnClient().delete(`/webhooks/${webhookId}`);
    }

    /**
     * Verify a webhook endpoint
     */
    async verifyWebhook(webhookId: string): Promise<{
        status: 'verified' | 'failed';
        response_code?: number;
        response_body?: string;
    }> {
        return await columnClient().post(`/webhooks/${webhookId}/verify`);
    }

    // ============================================================================
    // WEBHOOK DELIVERIES
    // ============================================================================

    /**
     * List webhook deliveries
     */
    async listWebhookDeliveries(params?: {
        webhook_id?: string;
        event_id?: string;
        status?: 'pending' | 'delivered' | 'failed';
        limit?: number;
        cursor?: string;
    }): Promise<ColumnListResponse<WebhookDelivery>> {
        return await columnClient().get('/webhook_deliveries', { params });
    }

    /**
     * Get webhook deliveries for a specific event
     */
    async getEventDeliveries(eventId: string): Promise<ColumnListResponse<WebhookDelivery>> {
        return await columnClient().get(`/events/${eventId}/webhook_deliveries`);
    }

    // ============================================================================
    // EVENT MANAGEMENT
    // ============================================================================

    /**
     * List all events
     */
    async listEvents(params?: {
        type?: string;
        start_date?: string;
        end_date?: string;
        limit?: number;
        cursor?: string;
    }): Promise<ColumnListResponse<ColumnEvent>> {
        return await columnClient().get('/events', { params });
    }

    /**
     * Get an event by ID
     */
    async getEvent(eventId: string): Promise<ColumnEvent> {
        return await columnClient().get(`/events/${eventId}`);
    }

    // ============================================================================
    // WEBHOOK SIGNATURE VERIFICATION
    // ============================================================================

    /**
     * Verify webhook signature
     */
    verifyWebhookSignature(
        payload: string,
        signature: string,
        secret?: string
    ): boolean {
        try {
            const webhookSecret = secret || this.webhookSecret;
            
            if (!webhookSecret) {
                console.warn('No webhook secret configured for signature verification');
                return false;
            }

            // Column uses HMAC-SHA256 for webhook signatures
            const expectedSignature = crypto
                .createHmac('sha256', webhookSecret)
                .update(payload, 'utf8')
                .digest('hex');

            // Column sends signature in format: sha256=<signature>
            const receivedSignature = signature.replace('sha256=', '');

            return crypto.timingSafeEqual(
                Buffer.from(expectedSignature, 'hex'),
                Buffer.from(receivedSignature, 'hex')
            );
        } catch (error) {
            console.error('Error verifying webhook signature:', error);
            return false;
        }
    }

    /**
     * Parse and verify webhook payload
     */
    parseWebhookPayload(
        body: string,
        signature: string,
        secret?: string
    ): ColumnEvent | null {
        try {
            // Verify signature first
            if (!this.verifyWebhookSignature(body, signature, secret)) {
                console.error('Invalid webhook signature');
                return null;
            }

            // Parse the JSON payload
            const event = JSON.parse(body) as ColumnEvent;
            
            // Basic validation
            if (!event.id || !event.type || !event.created_at) {
                console.error('Invalid webhook payload structure');
                return null;
            }

            return event;
        } catch (error) {
            console.error('Error parsing webhook payload:', error);
            return null;
        }
    }

    // ============================================================================
    // EVENT HANDLERS
    // ============================================================================

    /**
     * Handle different types of Column events
     */
    async handleEvent(event: ColumnEvent): Promise<void> {
        console.log(`Processing Column event: ${event.type} (${event.id})`);

        try {
            switch (event.type) {
                case 'entity.created':
                    await this.handleEntityCreated(event);
                    break;
                
                case 'entity.updated':
                    await this.handleEntityUpdated(event);
                    break;
                
                case 'bank_account.created':
                    await this.handleBankAccountCreated(event);
                    break;
                
                case 'bank_account.updated':
                    await this.handleBankAccountUpdated(event);
                    break;
                
                case 'transfer.created':
                    await this.handleTransferCreated(event);
                    break;
                
                case 'transfer.updated':
                    await this.handleTransferUpdated(event);
                    break;
                
                case 'transfer.completed':
                    await this.handleTransferCompleted(event);
                    break;
                
                case 'transfer.failed':
                    await this.handleTransferFailed(event);
                    break;
                
                default:
                    console.log(`Unhandled event type: ${event.type}`);
            }
        } catch (error) {
            console.error(`Error handling event ${event.id}:`, error);
            throw error;
        }
    }

    /**
     * Handle entity created events
     */
    private async handleEntityCreated(event: ColumnEvent): Promise<void> {
        const entity = event.data;
        console.log(`Entity created: ${entity.id} (${entity.type})`);
        
        // TODO: Update local database with new entity
        // TODO: Send notifications if needed
    }

    /**
     * Handle entity updated events
     */
    private async handleEntityUpdated(event: ColumnEvent): Promise<void> {
        const entity = event.data;
        console.log(`Entity updated: ${entity.id} - Status: ${entity.status}`);
        
        // TODO: Update local database
        // TODO: Handle status changes (approved, rejected, etc.)
    }

    /**
     * Handle bank account created events
     */
    private async handleBankAccountCreated(event: ColumnEvent): Promise<void> {
        const account = event.data;
        console.log(`Bank account created: ${account.id}`);
        
        // TODO: Update local database
        // TODO: Generate account numbers if needed
    }

    /**
     * Handle bank account updated events
     */
    private async handleBankAccountUpdated(event: ColumnEvent): Promise<void> {
        const account = event.data;
        console.log(`Bank account updated: ${account.id} - Status: ${account.status}`);
        
        // TODO: Update local database
        // TODO: Handle status changes
    }

    /**
     * Handle transfer created events
     */
    private async handleTransferCreated(event: ColumnEvent): Promise<void> {
        const transfer = event.data;
        console.log(`Transfer created: ${transfer.id} - Amount: $${transfer.amount}`);
        
        // TODO: Update local database
        // TODO: Send notifications to users
    }

    /**
     * Handle transfer updated events
     */
    private async handleTransferUpdated(event: ColumnEvent): Promise<void> {
        const transfer = event.data;
        console.log(`Transfer updated: ${transfer.id} - Status: ${transfer.status}`);
        
        // TODO: Update local database
        // TODO: Update UI in real-time
    }

    /**
     * Handle transfer completed events
     */
    private async handleTransferCompleted(event: ColumnEvent): Promise<void> {
        const transfer = event.data;
        console.log(`Transfer completed: ${transfer.id} - Amount: $${transfer.amount}`);
        
        // TODO: Update local database
        // TODO: Send completion notifications
        // TODO: Update account balances
    }

    /**
     * Handle transfer failed events
     */
    private async handleTransferFailed(event: ColumnEvent): Promise<void> {
        const transfer = event.data;
        console.log(`Transfer failed: ${transfer.id} - Reason: ${transfer.failure_reason}`);
        
        // TODO: Update local database
        // TODO: Send failure notifications
        // TODO: Handle retry logic if applicable
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * Get available event types
     */
    getAvailableEventTypes(): string[] {
        return [
            'entity.created',
            'entity.updated',
            'entity.approved',
            'entity.rejected',
            'bank_account.created',
            'bank_account.updated',
            'bank_account.closed',
            'transfer.created',
            'transfer.updated',
            'transfer.completed',
            'transfer.failed',
            'transfer.cancelled',
            'ach_transfer.returned',
            'wire_transfer.returned',
            'counterparty.created',
            'counterparty.updated',
            'document.uploaded',
            'document.approved',
            'document.rejected'
        ];
    }

    /**
     * Validate webhook URL
     */
    validateWebhookUrl(url: string): boolean {
        try {
            const parsedUrl = new URL(url);
            return parsedUrl.protocol === 'https:';
        } catch {
            return false;
        }
    }
}

// Export singleton instance
export const columnWebhookService = new ColumnWebhookService();
