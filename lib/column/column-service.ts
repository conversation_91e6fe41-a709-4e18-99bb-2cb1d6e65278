import { columnClient } from './column-client';
import {
    PersonEntity,
    BusinessEntity,
    BankAccount,
    AccountNumber,
    Transfer,
    Counterparty,
    CreatePersonEntityRequest,
    CreateBusinessEntityRequest,
    CreateBankAccountRequest,
    CreateACHTransferRequest,
    CreateWireTransferRequest,
    CreateCounterpartyRequest,
    ColumnListResponse,
    BankAccountSummary,
    FinancialInstitution
} from '@/types/column.types';

export class ColumnService {
    
    // ============================================================================
    // ENTITY MANAGEMENT
    // ============================================================================

    /**
     * Create a person entity
     */
    async createPersonEntity(data: CreatePersonEntityRequest): Promise<PersonEntity> {
        return await columnClient().post('/entities/person', data);
    }

    /**
     * Create a business entity
     */
    async createBusinessEntity(data: CreateBusinessEntityRequest): Promise<BusinessEntity> {
        return await columnClient().post('/entities/business', data);
    }

    /**
     * Get an entity by ID
     */
    async getEntity(entityId: string): Promise<PersonEntity | BusinessEntity> {
        return await columnClient().get(`/entities/${entityId}`);
    }

    /**
     * List all entities
     */
    async listEntities(params?: {
        limit?: number;
        cursor?: string;
        status?: string;
        type?: 'person' | 'business';
    }): Promise<ColumnListResponse<PersonEntity | BusinessEntity>> {
        return await columnClient().get('/entities', { params });
    }

    /**
     * Update a person entity
     */
    async updatePersonEntity(entityId: string, data: Partial<CreatePersonEntityRequest>): Promise<PersonEntity> {
        return await columnClient().put(`/entities/person/${entityId}`, data);
    }

    /**
     * Update a business entity
     */
    async updateBusinessEntity(entityId: string, data: Partial<CreateBusinessEntityRequest>): Promise<BusinessEntity> {
        return await columnClient().put(`/entities/business/${entityId}`, data);
    }

    /**
     * Delete an entity
     */
    async deleteEntity(entityId: string): Promise<void> {
        await columnClient().delete(`/entities/${entityId}`);
    }

    // ============================================================================
    // BANK ACCOUNT MANAGEMENT
    // ============================================================================

    /**
     * Create a new bank account
     */
    async createBankAccount(data: CreateBankAccountRequest): Promise<BankAccount> {
        return await columnClient().post('/bank_accounts', data);
    }

    /**
     * Get a bank account by ID
     */
    async getBankAccount(accountId: string): Promise<BankAccount> {
        return await columnClient().get(`/bank_accounts/${accountId}`);
    }

    /**
     * List all bank accounts
     */
    async listBankAccounts(params?: {
        limit?: number;
        cursor?: string;
        entity_id?: string;
        status?: string;
    }): Promise<ColumnListResponse<BankAccount>> {
        return await columnClient().get('/bank_accounts', { params });
    }

    /**
     * Update a bank account
     */
    async updateBankAccount(accountId: string, data: {
        nickname?: string;
        is_overdraftable?: boolean;
        overdraft_limit?: number;
    }): Promise<BankAccount> {
        return await columnClient().put(`/bank_accounts/${accountId}`, data);
    }

    /**
     * Close a bank account
     */
    async closeBankAccount(accountId: string): Promise<BankAccount> {
        return await columnClient().delete(`/bank_accounts/${accountId}`);
    }

    /**
     * Get bank account summary history
     */
    async getBankAccountSummary(accountId: string, params?: {
        start_date?: string;
        end_date?: string;
    }): Promise<ColumnListResponse<BankAccountSummary>> {
        return await columnClient().get(`/bank_accounts/${accountId}/summary`, { params });
    }

    // ============================================================================
    // ACCOUNT NUMBERS
    // ============================================================================

    /**
     * Create a new account number for a bank account
     */
    async createAccountNumber(bankAccountId: string): Promise<AccountNumber> {
        return await columnClient().post('/account_numbers', {
            bank_account_id: bankAccountId
        });
    }

    /**
     * List account numbers for a bank account
     */
    async listAccountNumbers(bankAccountId: string): Promise<ColumnListResponse<AccountNumber>> {
        return await columnClient().get(`/bank_accounts/${bankAccountId}/account_numbers`);
    }

    /**
     * Get an account number
     */
    async getAccountNumber(accountNumberId: string): Promise<AccountNumber> {
        return await columnClient().get(`/account_numbers/${accountNumberId}`);
    }

    // ============================================================================
    // COUNTERPARTY MANAGEMENT
    // ============================================================================

    /**
     * Create a counterparty
     */
    async createCounterparty(data: CreateCounterpartyRequest): Promise<Counterparty> {
        return await columnClient().post('/counterparties', data);
    }

    /**
     * Get a counterparty by ID
     */
    async getCounterparty(counterpartyId: string): Promise<Counterparty> {
        return await columnClient().get(`/counterparties/${counterpartyId}`);
    }

    /**
     * List all counterparties
     */
    async listCounterparties(params?: {
        limit?: number;
        cursor?: string;
    }): Promise<ColumnListResponse<Counterparty>> {
        return await columnClient().get('/counterparties', { params });
    }

    /**
     * Delete a counterparty
     */
    async deleteCounterparty(counterpartyId: string): Promise<void> {
        await columnClient().delete(`/counterparties/${counterpartyId}`);
    }

    /**
     * Validate bank account information
     */
    async validateBankAccount(routingNumber: string, accountNumber: string): Promise<{
        is_valid: boolean;
        bank_name?: string;
        account_type?: string;
    }> {
        return await columnClient().post('/counterparties/validate', {
            routing_number: routingNumber,
            account_number: accountNumber
        });
    }

    // ============================================================================
    // FINANCIAL INSTITUTIONS
    // ============================================================================

    /**
     * Get financial institution by routing number
     */
    async getFinancialInstitution(routingNumber: string): Promise<FinancialInstitution> {
        return await columnClient().get(`/financial_institutions/${routingNumber}`);
    }

    /**
     * List financial institutions
     */
    async listFinancialInstitutions(params?: {
        limit?: number;
        cursor?: string;
        search?: string;
    }): Promise<ColumnListResponse<FinancialInstitution>> {
        return await columnClient().get('/financial_institutions', { params });
    }

    // ============================================================================
    // TRANSFER MANAGEMENT
    // ============================================================================

    /**
     * Create an ACH transfer
     */
    async createACHTransfer(data: CreateACHTransferRequest): Promise<Transfer> {
        return await columnClient().post('/ach_transfers', data);
    }

    /**
     * Create a wire transfer
     */
    async createWireTransfer(data: CreateWireTransferRequest): Promise<Transfer> {
        return await columnClient().post('/wire_transfers', data);
    }

    /**
     * Create a book transfer (internal transfer between Column accounts)
     */
    async createBookTransfer(data: {
        from_account_id: string;
        to_account_id: string;
        amount: number;
        description: string;
    }): Promise<Transfer> {
        return await columnClient().post('/book_transfers', data);
    }

    /**
     * Get a transfer by ID
     */
    async getTransfer(transferId: string): Promise<Transfer> {
        return await columnClient().get(`/transfers/${transferId}`);
    }

    /**
     * List all transfers
     */
    async listTransfers(params?: {
        limit?: number;
        cursor?: string;
        account_id?: string;
        type?: string;
        status?: string;
        start_date?: string;
        end_date?: string;
    }): Promise<ColumnListResponse<Transfer>> {
        return await columnClient().get('/transfers', { params });
    }

    /**
     * Cancel a transfer (if possible)
     */
    async cancelTransfer(transferId: string): Promise<Transfer> {
        return await columnClient().post(`/transfers/${transferId}/cancel`);
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * Get organizations for dropdown (business entities)
     */
    async getOrganizations(): Promise<{ label: string; value: string }[]> {
        try {
            const response = await this.listEntities({
                type: 'business',
                limit: 50
            });

            // Column API returns entities in 'entities' field, not 'data'
            const entities = (response as any).entities || response.data || [];

            return entities.map((entity: any) => ({
                label: entity.name || entity.legal_name || entity.dba_name || `Business ${entity.id.slice(-8)}`,
                value: entity.id
            }));
        } catch (error) {
            console.error('Failed to fetch organizations:', error);
            // Return fallback organizations
            return [
                { label: 'CONNECTING MATRIX LLC', value: 'default-1' },
                { label: 'HELLO CORP', value: 'default-2' },
                { label: 'FALLOUT', value: 'default-3' }
            ];
        }
    }

    /**
     * Check if Column API is healthy
     */
    async healthCheck(): Promise<boolean> {
        return await columnClient().healthCheck();
    }

    /**
     * Get current environment
     */
    getEnvironment(): string {
        return columnClient().getEnvironment();
    }

    /**
     * Check if in sandbox mode
     */
    isSandbox(): boolean {
        return columnClient().isSandbox();
    }

    /**
     * Check if Column is properly configured
     */
    isConfigured(): boolean {
        return columnClient().isConfigured();
    }

    // ============================================================================
    // SANDBOX SIMULATION METHODS
    // ============================================================================

    /**
     * Simulate receiving an ACH credit (sandbox only)
     */
    async simulateACHCredit(data: {
        account_id: string;
        amount: number;
        counterparty_name: string;
        counterparty_account_number: string;
        counterparty_routing_number: string;
        description?: string;
    }): Promise<Transfer> {
        if (!this.isSandbox()) {
            throw new Error('Simulation methods are only available in sandbox environment');
        }
        return await columnClient().post('/simulation/ach_credit', data);
    }

    /**
     * Simulate receiving an ACH debit (sandbox only)
     */
    async simulateACHDebit(data: {
        account_id: string;
        amount: number;
        counterparty_name: string;
        counterparty_account_number: string;
        counterparty_routing_number: string;
        description?: string;
    }): Promise<Transfer> {
        if (!this.isSandbox()) {
            throw new Error('Simulation methods are only available in sandbox environment');
        }
        return await columnClient().post('/simulation/ach_debit', data);
    }

    /**
     * Simulate receiving a wire transfer (sandbox only)
     */
    async simulateWireTransfer(data: {
        account_id: string;
        amount: number;
        sender_name: string;
        sender_bank_name: string;
        description?: string;
    }): Promise<Transfer> {
        if (!this.isSandbox()) {
            throw new Error('Simulation methods are only available in sandbox environment');
        }
        return await columnClient().post('/simulation/wire_transfer', data);
    }

    /**
     * Simulate settling an ACH transfer (sandbox only)
     */
    async simulateACHSettle(transferId: string): Promise<Transfer> {
        if (!this.isSandbox()) {
            throw new Error('Simulation methods are only available in sandbox environment');
        }
        return await columnClient().post(`/simulation/ach_transfers/${transferId}/settle`);
    }

    /**
     * Simulate settling a wire transfer (sandbox only)
     */
    async simulateWireSettle(transferId: string): Promise<Transfer> {
        if (!this.isSandbox()) {
            throw new Error('Simulation methods are only available in sandbox environment');
        }
        return await columnClient().post(`/simulation/wire_transfers/${transferId}/settle`);
    }
}

// Export singleton instance
export const columnService = new ColumnService();
