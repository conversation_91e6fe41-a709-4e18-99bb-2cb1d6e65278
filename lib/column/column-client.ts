import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

export class ColumnClient {
    private client: AxiosInstance | null = null;
    private apiKey: string = '';
    private baseURL: string = '';
    private environment: string = '';
    private initialized: boolean = false;

    private initialize() {
        if (this.initialized) return;

        // Get environment variables (works in both server and client)
        this.apiKey = process.env.COLUMN_API_KEY || '';
        this.baseURL = process.env.COLUMN_BASE_URL || 'https://api.column.com';
        this.environment = process.env.COLUMN_ENVIRONMENT || 'sandbox';

        if (!this.apiKey) {
            console.warn('COLUMN_API_KEY environment variable is not set. Column features will be disabled.');
            this.initialized = true;
            return;
        }

        // Validate API key format only if we have one
        if (this.environment === 'production' && this.apiKey && !this.apiKey.startsWith('live_')) {
            console.error('Production environment requires a live API key (live_...)');
            this.initialized = true;
            return;
        }

        if (this.environment === 'sandbox' && this.apiKey && !this.apiKey.startsWith('test_')) {
            console.error('Sandbox environment requires a test API key (test_...)');
            this.initialized = true;
            return;
        }

        this.client = axios.create({
            baseURL: this.baseURL,
            auth: {
                username: '',
                password: this.apiKey
            },
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'equate-banking-app/1.0'
            },
            timeout: 30000
        });

        // Request interceptor for logging and debugging
        this.client.interceptors.request.use((config) => {
            if (process.env.NODE_ENV === 'development') {
                console.log(`Column API Request: ${config.method?.toUpperCase()} ${config.url}`);
                if (config.data) {
                    console.log('Request Data:', JSON.stringify(config.data, null, 2));
                }
            }
            return config;
        });

        // Response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`Column API Response: ${response.status} ${response.statusText}`);
                }
                return response;
            },
            (error) => {
                const errorMessage = error.response?.data?.error?.message || error.message;
                const errorCode = error.response?.data?.error?.code || 'UNKNOWN_ERROR';

                console.error('Column API Error:', {
                    code: errorCode,
                    message: errorMessage,
                    status: error.response?.status,
                    url: error.config?.url,
                    method: error.config?.method
                });

                // Transform Column API errors to a consistent format
                const transformedError = new Error(errorMessage);
                (transformedError as any).code = errorCode;
                (transformedError as any).status = error.response?.status;
                (transformedError as any).details = error.response?.data?.error?.details;

                throw transformedError;
            }
        );

        this.initialized = true;
    }

    /**
     * Get the current environment (sandbox or production)
     */
    getEnvironment(): string {
        this.initialize();
        return this.environment;
    }

    /**
     * Check if the client is in sandbox mode
     */
    isSandbox(): boolean {
        this.initialize();
        return this.environment === 'sandbox';
    }

    /**
     * Check if Column is properly configured
     */
    isConfigured(): boolean {
        this.initialize();
        return !!this.apiKey && !!this.client;
    }

    /**
     * Make a GET request to the Column API
     */
    async get<T = any>(endpoint: string, config?: AxiosRequestConfig): Promise<T> {
        if (!this.isConfigured()) {
            throw new Error('Column API is not configured. Please set COLUMN_API_KEY environment variable.');
        }
        const response = await this.client!.get(endpoint, config);
        return response.data;
    }

    /**
     * Make a POST request to the Column API
     */
    async post<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        if (!this.isConfigured()) {
            throw new Error('Column API is not configured. Please set COLUMN_API_KEY environment variable.');
        }
        const response = await this.client!.post(endpoint, data, config);
        return response.data;
    }

    /**
     * Make a PUT request to the Column API
     */
    async put<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        if (!this.isConfigured()) {
            throw new Error('Column API is not configured. Please set COLUMN_API_KEY environment variable.');
        }
        const response = await this.client!.put(endpoint, data, config);
        return response.data;
    }

    /**
     * Make a PATCH request to the Column API
     */
    async patch<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        if (!this.isConfigured()) {
            throw new Error('Column API is not configured. Please set COLUMN_API_KEY environment variable.');
        }
        const response = await this.client!.patch(endpoint, data, config);
        return response.data;
    }

    /**
     * Make a DELETE request to the Column API
     */
    async delete<T = any>(endpoint: string, config?: AxiosRequestConfig): Promise<T> {
        if (!this.isConfigured()) {
            throw new Error('Column API is not configured. Please set COLUMN_API_KEY environment variable.');
        }
        const response = await this.client!.delete(endpoint, config);
        return response.data;
    }

    /**
     * Handle paginated requests
     */
    async getPaginated<T = any>(
        endpoint: string, 
        params?: Record<string, any>
    ): Promise<{ data: T[]; hasMore: boolean; nextCursor?: string }> {
        const response = await this.get(endpoint, { params });
        
        return {
            data: response.data || [],
            hasMore: response.has_more || false,
            nextCursor: response.next_cursor
        };
    }

    /**
     * Get all pages of a paginated endpoint
     */
    async getAllPages<T = any>(endpoint: string, params?: Record<string, any>): Promise<T[]> {
        const allData: T[] = [];
        let cursor: string | undefined;
        let hasMore = true;

        while (hasMore) {
            const requestParams = { ...params };
            if (cursor) {
                requestParams.cursor = cursor;
            }

            const response = await this.getPaginated<T>(endpoint, requestParams);
            allData.push(...response.data);
            
            hasMore = response.hasMore;
            cursor = response.nextCursor;
        }

        return allData;
    }

    /**
     * Upload a file (for document submissions)
     */
    async uploadFile(endpoint: string, file: File | Buffer, filename: string): Promise<any> {
        const formData = new FormData();
        
        if (file instanceof File) {
            formData.append('file', file, filename);
        } else {
            // Handle Buffer for server-side uploads
            const blob = new Blob([file]);
            formData.append('file', blob, filename);
        }

        const response = await this.client!.post(endpoint, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        return response.data;
    }

    /**
     * Health check to verify API connectivity
     */
    async healthCheck(): Promise<boolean> {
        if (!this.isConfigured()) {
            return false;
        }

        try {
            // Try to list entities as a simple health check
            await this.get('/entities', { params: { limit: 1 } });
            return true;
        } catch (error) {
            console.error('Column API health check failed:', error);
            return false;
        }
    }

    /**
     * Get API rate limit information from response headers
     */
    getRateLimitInfo(response: any): {
        limit: number;
        remaining: number;
        resetTime: Date;
    } | null {
        const headers = response.headers;
        
        if (headers['x-ratelimit-limit']) {
            return {
                limit: parseInt(headers['x-ratelimit-limit']),
                remaining: parseInt(headers['x-ratelimit-remaining']),
                resetTime: new Date(parseInt(headers['x-ratelimit-reset']) * 1000)
            };
        }
        
        return null;
    }
}

// Export a singleton instance (lazy-loaded)
let _columnClient: ColumnClient | null = null;

export const columnClient = (): ColumnClient => {
    if (!_columnClient) {
        _columnClient = new ColumnClient();
    }
    return _columnClient;
};
