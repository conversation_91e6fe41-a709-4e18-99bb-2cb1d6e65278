import { createClient } from '@/lib/supabase/client'
import { Database } from '@/types/database.types'

type Profile = Database['public']['Tables']['profiles']['Row']

export class AuthService {
  private supabase = createClient()

  async signUp(email: string, password: string, userData: {
    full_name: string
    role?: Profile['role']
    employment_entity?: string
  }) {
    const { data, error } = await this.supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: userData.full_name,
          role: userData.role || 'employee',
          employment_entity: userData.employment_entity
        }
      }
    })

    if (error) {
      throw new Error(`Sign up failed: ${error.message}`)
    }

    // Profile creation will be handled by database triggers or server-side functions
    return data
  }

  async signIn(email: string, password: string) {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password
    })

    console.log('data', data);
    console.log('error', error);


    if (error) {
      throw new Error(`Sign in failed: ${error.message}`)
    }

    return data
  }

  async signOut() {
    const { error } = await this.supabase.auth.signOut()

    if (error) {
      throw new Error(`Sign out failed: ${error.message}`)
    }
  }

  async getCurrentUser() {
    const { data: { user }, error } = await this.supabase.auth.getUser()

    if (error) {
      throw new Error(`Failed to get current user: ${error.message}`)
    }

    return user
  }

  async getCurrentUserProfile(): Promise<Profile | null> {
    const user = await this.getCurrentUser()

    if (!user) {
      return null
    }

    // Get user profile from database
    const { data, error } = await this.supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) {
      console.error('Failed to fetch user profile:', error)
      return null
    }

    return data
  }

  async updatePassword(newPassword: string) {
    const { error } = await this.supabase.auth.updateUser({
      password: newPassword
    })

    if (error) {
      throw new Error(`Password update failed: ${error.message}`)
    }
  }

  async resetPassword(email: string) {
    const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`
    })

    if (error) {
      throw new Error(`Password reset failed: ${error.message}`)
    }
  }

  async inviteUser(_email: string, _userData: {
    full_name: string
    role: Profile['role']
    employment_entity: string
  }) {
    // Note: User invitation with admin privileges should be done server-side
    // For now, we'll throw an error to indicate this needs server-side implementation
    throw new Error('User invitation must be implemented server-side with admin privileges')
  }

  async updateUserProfile(updates: Partial<Profile>) {
    const user = await this.getCurrentUser()

    if (!user) {
      throw new Error('No authenticated user found')
    }

    // Update user profile in database
    const { data, error } = await this.supabase
      .from('profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', user.id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update profile: ${error.message}`)
    }

    return data
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return this.supabase.auth.onAuthStateChange(callback)
  }
}
