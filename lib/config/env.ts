// Environment configuration with validation

interface EnvironmentConfig {
  supabase: {
    url: string
    anonKey: string
    serviceRoleKey?: string
  }
  app: {
    name: string
    url: string
    environment: 'development' | 'staging' | 'production'
  }
  auth: {
    secret?: string
    url: string
  }
  database: {
    url?: string
  }
  column: {
    apiKey?: string
  }
}

function getRequiredEnvVar(name: string): string {
  const value = process.env[name]
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`)
  }
  return value
}

function getOptionalEnvVar(name: string, defaultValue?: string): string | undefined {
  return process.env[name] || defaultValue
}

export const env: EnvironmentConfig = {
  supabase: {
    url: getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
    anonKey: getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
    serviceRoleKey: getOptionalEnvVar('SUPABASE_SERVICE_ROLE_KEY')
  },
  app: {
    name: getOptionalEnvVar('NEXT_PUBLIC_APP_NAME', 'equate') || 'equate',
    url: getOptionalEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000') || 'http://localhost:3000',
    environment: (getOptionalEnvVar('NODE_ENV', 'development') as 'development' | 'staging' | 'production') || 'development'
  },
  auth: {
    secret: getOptionalEnvVar('NEXTAUTH_SECRET'),
    url: getOptionalEnvVar('NEXTAUTH_URL', 'http://localhost:3000') || 'http://localhost:3000'
  },
  database: {
    url: getOptionalEnvVar('DATABASE_URL')
  },
  column: {
    apiKey: getOptionalEnvVar('COLUMN_API_KEY')
  }
}

// Validation function
export function validateEnvironment(): void {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'COLUMN_API_KEY'
  ]

  const missingVars = requiredVars.filter(varName => !process.env[varName])

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.'
    )
  }

  // Validate URLs
  try {
    new URL(env.supabase.url)
    new URL(env.app.url)
  } catch (error) {
    throw new Error('Invalid URL format in environment variables')
  }

  console.log('✅ Environment configuration validated successfully')
}

// Development helpers
export const isDevelopment = env.app.environment === 'development'
export const isProduction = env.app.environment === 'production'
export const isStaging = env.app.environment === 'staging'

// Export individual configs for convenience
export const supabaseConfig = env.supabase
export const appConfig = env.app
export const authConfig = env.auth
export const databaseConfig = env.database
