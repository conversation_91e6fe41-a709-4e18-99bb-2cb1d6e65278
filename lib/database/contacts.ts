import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database.types'

type Contact = Database['public']['Tables']['contacts']['Row']
type ContactInsert = Database['public']['Tables']['contacts']['Insert']
type ContactUpdate = Database['public']['Tables']['contacts']['Update']

export class ContactService {
  private supabase = createClient()

  async getContacts(): Promise<Contact[]> {
    const { data, error } = await this.supabase
      .from('contacts')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch contacts: ${error.message}`)
    }

    return data || []
  }

  async getContactById(id: string): Promise<Contact | null> {
    const { data, error } = await this.supabase
      .from('contacts')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Contact not found
      }
      throw new Error(`Failed to fetch contact: ${error.message}`)
    }

    return data
  }

  async getContactsByOrganization(organizationId: string): Promise<Contact[]> {
    const { data, error } = await this.supabase
      .from('contacts')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch contacts: ${error.message}`)
    }

    return data || []
  }

  async createContact(contact: ContactInsert): Promise<Contact> {
    const { data, error } = await this.supabase
      .from('contacts')
      .insert(contact)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create contact: ${error.message}`)
    }

    return data
  }

  async updateContact(id: string, updates: ContactUpdate): Promise<Contact> {
    const { data, error } = await this.supabase
      .from('contacts')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update contact: ${error.message}`)
    }

    return data
  }

  async deleteContact(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('contacts')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete contact: ${error.message}`)
    }
  }

  async searchContacts(organizationId: string, query: string): Promise<Contact[]> {
    const { data, error } = await this.supabase
      .from('contacts')
      .select('*')
      .eq('organization_id', organizationId)
      .or(`name.ilike.%${query}%,email.ilike.%${query}%`)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to search contacts: ${error.message}`)
    }

    return data || []
  }

  async getContactByEmail(organizationId: string, email: string): Promise<Contact | null> {
    const { data, error } = await this.supabase
      .from('contacts')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('email', email)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Contact not found
      }
      throw new Error(`Failed to fetch contact: ${error.message}`)
    }

    return data
  }
}
