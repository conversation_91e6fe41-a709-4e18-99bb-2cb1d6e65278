import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database.types'

type Transfer = Database['public']['Tables']['transfers']['Row']
type TransferInsert = Database['public']['Tables']['transfers']['Insert']
type TransferUpdate = Database['public']['Tables']['transfers']['Update']

export class TransferService {
  private supabase = createClient()

  async getTransfers(): Promise<Transfer[]> {
    const { data, error } = await this.supabase
      .from('transfers')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transfers: ${error.message}`)
    }

    return data || []
  }

  async getTransferById(id: string): Promise<Transfer | null> {
    const { data, error } = await this.supabase
      .from('transfers')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Transfer not found
      }
      throw new Error(`Failed to fetch transfer: ${error.message}`)
    }

    return data
  }

  async getTransfersByAccount(accountId: string): Promise<Transfer[]> {
    const { data, error } = await this.supabase
      .from('transfers')
      .select('*')
      .or(`from_account_id.eq.${accountId},to_account_id.eq.${accountId}`)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transfers: ${error.message}`)
    }

    return data || []
  }

  async createTransfer(transfer: TransferInsert): Promise<Transfer> {
    const { data, error } = await this.supabase
      .from('transfers')
      .insert(transfer)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create transfer: ${error.message}`)
    }

    return data
  }

  async updateTransfer(id: string, updates: TransferUpdate): Promise<Transfer> {
    const { data, error } = await this.supabase
      .from('transfers')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update transfer: ${error.message}`)
    }

    return data
  }

  async deleteTransfer(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('transfers')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete transfer: ${error.message}`)
    }
  }

  async getTransfersByStatus(status: Transfer['status']): Promise<Transfer[]> {
    const { data, error } = await this.supabase
      .from('transfers')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transfers by status: ${error.message}`)
    }

    return data || []
  }

  async getTransfersByUser(userId: string): Promise<Transfer[]> {
    const { data, error } = await this.supabase
      .from('transfers')
      .select('*')
      .eq('created_by', userId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transfers by user: ${error.message}`)
    }

    return data || []
  }

  async getTransferStats(): Promise<{
    total: number
    pending: number
    processing: number
    completed: number
    failed: number
    totalAmount: number
  }> {
    const { data, error } = await this.supabase
      .from('transfers')
      .select('status, amount')

    if (error) {
      throw new Error(`Failed to fetch transfer stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      totalAmount: 0
    }

    data.forEach(transfer => {
      stats[transfer.status]++
      if (transfer.status === 'completed') {
        stats.totalAmount += transfer.amount
      }
    })

    return stats
  }
}
