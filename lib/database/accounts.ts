import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database.types'

type Account = Database['public']['Tables']['accounts']['Row']
type AccountInsert = Database['public']['Tables']['accounts']['Insert']
type AccountUpdate = Database['public']['Tables']['accounts']['Update']

export class AccountService {
  private supabase = createClient()

  async getAccounts(): Promise<Account[]> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`)
    }

    return data || []
  }

  async getAccountById(id: string): Promise<Account | null> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Account not found
      }
      throw new Error(`Failed to fetch account: ${error.message}`)
    }

    return data
  }

  async getAccountsByOrganization(organizationId: string): Promise<Account[]> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`)
    }

    return data || []
  }

  async createAccount(account: AccountInsert): Promise<Account> {
    const { data, error } = await this.supabase
      .from('accounts')
      .insert(account)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create account: ${error.message}`)
    }

    return data
  }

  async updateAccount(id: string, updates: AccountUpdate): Promise<Account> {
    const { data, error } = await this.supabase
      .from('accounts')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update account: ${error.message}`)
    }

    return data
  }

  async deleteAccount(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('accounts')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete account: ${error.message}`)
    }
  }

  async getAccountBalance(id: string): Promise<number> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('balance')
      .eq('id', id)
      .single()

    if (error) {
      throw new Error(`Failed to fetch account balance: ${error.message}`)
    }

    return data.balance
  }

  async updateAccountBalance(id: string, newBalance: number): Promise<Account> {
    const { data, error } = await this.supabase
      .from('accounts')
      .update({ 
        balance: newBalance, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update account balance: ${error.message}`)
    }

    return data
  }
}
