// Database services export
export { UserService } from './users'
export { OrganizationService } from './organizations'
export { AccountService } from './accounts'
export { TransactionService } from './transactions'
export { TransferService } from './transfers'
export { ContactService } from './contacts'

// Combined database service
import { UserService } from './users'
import { OrganizationService } from './organizations'
import { AccountService } from './accounts'
import { TransactionService } from './transactions'
import { TransferService } from './transfers'
import { ContactService } from './contacts'

export class DatabaseService {
  public users: UserService
  public organizations: OrganizationService
  public accounts: AccountService
  public transactions: TransactionService
  public transfers: TransferService
  public contacts: ContactService

  constructor() {
    this.users = new UserService()
    this.organizations = new OrganizationService()
    this.accounts = new AccountService()
    this.transactions = new TransactionService()
    this.transfers = new TransferService()
    this.contacts = new ContactService()
  }

  // Dashboard analytics methods
  async getDashboardStats(organizationId: string) {
    try {
      const [
        accounts,
        userStats,
        transferStats,
        recentTransactions
      ] = await Promise.all([
        this.accounts.getAccountsByOrganization(organizationId),
        this.users.getUserStats(),
        this.transfers.getTransferStats(),
        this.getRecentTransactions(organizationId, 10)
      ])

      const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0)
      const activeAccounts = accounts.filter(account => account.status === 'active').length

      return {
        totalBalance,
        activeAccounts,
        totalUsers: userStats.total,
        activeUsers: userStats.active,
        pendingTransfers: transferStats.pending,
        completedTransfers: transferStats.completed,
        recentTransactions,
        accounts: accounts.slice(0, 5) // Top 5 accounts
      }
    } catch (error) {
      throw new Error(`Failed to fetch dashboard stats: ${error}`)
    }
  }

  async getRecentTransactions(organizationId: string, limit: number = 10) {
    try {
      const accounts = await this.accounts.getAccountsByOrganization(organizationId)
      const accountIds = accounts.map(account => account.id)
      
      const allTransactions = await Promise.all(
        accountIds.map(accountId => this.transactions.getTransactionsByAccount(accountId))
      )

      const flatTransactions = allTransactions.flat()
      
      // Sort by created_at and limit
      return flatTransactions
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, limit)
    } catch (error) {
      throw new Error(`Failed to fetch recent transactions: ${error}`)
    }
  }

  async getAccountSummary(accountId: string) {
    try {
      const [
        account,
        transactions,
        transactionStats
      ] = await Promise.all([
        this.accounts.getAccountById(accountId),
        this.transactions.getTransactionsByAccount(accountId),
        this.transactions.getTransactionStats(accountId)
      ])

      if (!account) {
        throw new Error('Account not found')
      }

      const recentTransactions = transactions.slice(0, 10)
      
      return {
        account,
        recentTransactions,
        stats: transactionStats,
        transactionCount: transactions.length
      }
    } catch (error) {
      throw new Error(`Failed to fetch account summary: ${error}`)
    }
  }

  async getUserActivity(userId: string) {
    try {
      const [
        user,
        userTransfers,
        userContacts
      ] = await Promise.all([
        this.users.getUserById(userId),
        this.transfers.getTransfersByUser(userId),
        this.contacts.getContacts() // Filter by created_by in the service
      ])

      if (!user) {
        throw new Error('User not found')
      }

      const userCreatedContacts = userContacts.filter(contact => contact.created_by === userId)

      return {
        user,
        transfersCreated: userTransfers.length,
        contactsCreated: userCreatedContacts.length,
        recentTransfers: userTransfers.slice(0, 5),
        recentContacts: userCreatedContacts.slice(0, 5)
      }
    } catch (error) {
      throw new Error(`Failed to fetch user activity: ${error}`)
    }
  }

  // Utility methods for common operations
  async searchAll(organizationId: string, query: string) {
    try {
      const [
        users,
        contacts,
        transactions
      ] = await Promise.all([
        this.users.searchUsers(query),
        this.contacts.searchContacts(organizationId, query),
        this.getRecentTransactions(organizationId, 100) // Get more for search
      ])

      const filteredTransactions = transactions.filter(transaction =>
        transaction.description.toLowerCase().includes(query.toLowerCase()) ||
        transaction.reference?.toLowerCase().includes(query.toLowerCase())
      )

      return {
        users: users.slice(0, 10),
        contacts: contacts.slice(0, 10),
        transactions: filteredTransactions.slice(0, 10)
      }
    } catch (error) {
      throw new Error(`Failed to perform search: ${error}`)
    }
  }
}

// Export singleton instance
export const db = new DatabaseService()
