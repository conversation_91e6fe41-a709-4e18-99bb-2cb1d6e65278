-- Sample data for equate

-- Insert sample organization
INSERT INTO organizations (id, name, legal_entity_name, entity_registration_location, legal_entity_form, registered_office_address, ultimate_beneficial_owner) VALUES
('550e8400-e29b-41d4-a716-************', 'CONNECTING MATRIX LLC', 'CONNECTING MATRIX LLC', 'United States of America', 'Company', '5900 BALCONES DR 18462, AUSTIN, TX 78731 Austin TX 78731', ARRAY['<PERSON>ham Fatima Mir', '<PERSON>']);

-- Insert sample profiles (Note: These IDs should match actual auth.users IDs in production)
INSERT INTO profiles (id, email, full_name, role, status, employment_entity) VALUES
('123e4567-e89b-12d3-a456-************', '<EMAIL>', '<PERSON>', 'owner', 'active', 'CONNECTING MATRIX LLC'),
('123e4567-e89b-12d3-a456-************', 'mujab<PERSON><PERSON><EMAIL>', 'Mujab', 'admin', 'active', 'CONNECTING MATRIX LLC'),
('123e4567-e89b-12d3-a456-************', '<EMAIL>', 'Maham Fatima Mir', 'finance_admin', 'active', 'CONNECTING MATRIX LLC');

-- Update manager relationships
UPDATE profiles SET manager_id = '123e4567-e89b-12d3-a456-************' WHERE id = '123e4567-e89b-12d3-a456-************';
UPDATE profiles SET manager_id = '123e4567-e89b-12d3-a456-************' WHERE id = '123e4567-e89b-12d3-a456-************';

-- Insert sample accounts
INSERT INTO accounts (id, organization_id, account_name, account_type, currency, balance, status) VALUES
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'CONNECTING MATRIX LLC - Main Account', 'business', 'USD', 125000.00, 'active'),
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'CONNECTING MATRIX LLC - Savings', 'savings', 'USD', 50000.00, 'active');

-- Insert sample transactions
INSERT INTO transactions (account_id, type, amount, currency, description, category, status) VALUES
('650e8400-e29b-41d4-a716-************', 'credit', 10000.00, 'USD', 'Initial deposit', 'Capital', 'completed'),
('650e8400-e29b-41d4-a716-************', 'credit', 25000.00, 'USD', 'Client payment - Project Alpha', 'Revenue', 'completed'),
('650e8400-e29b-41d4-a716-************', 'debit', 5000.00, 'USD', 'Office rent payment', 'Operating Expenses', 'completed'),
('650e8400-e29b-41d4-a716-************', 'debit', 3500.00, 'USD', 'Software licenses', 'Technology', 'completed'),
('650e8400-e29b-41d4-a716-************', 'credit', 15000.00, 'USD', 'Client payment - Project Beta', 'Revenue', 'completed'),
('650e8400-e29b-41d4-a716-************', 'debit', 2000.00, 'USD', 'Marketing expenses', 'Marketing', 'completed'),
('650e8400-e29b-41d4-a716-************', 'credit', 50000.00, 'USD', 'Transfer from main account', 'Transfer', 'completed');

-- Insert sample contacts
INSERT INTO contacts (organization_id, name, email, phone, bank_details, created_by) VALUES
('550e8400-e29b-41d4-a716-************', 'Tech Solutions Inc', '<EMAIL>', '******-0123', '{"bank_name": "Chase Bank", "account_number": "****1234", "routing_number": "*********"}', '123e4567-e89b-12d3-a456-************'),
('550e8400-e29b-41d4-a716-************', 'Digital Marketing Pro', '<EMAIL>', '******-0456', '{"bank_name": "Bank of America", "account_number": "****5678", "routing_number": "*********"}', '123e4567-e89b-12d3-a456-************'),
('550e8400-e29b-41d4-a716-************', 'Cloud Services Ltd', '<EMAIL>', '******-0789', '{"bank_name": "Wells Fargo", "account_number": "****9012", "routing_number": "*********"}', '123e4567-e89b-12d3-a456-************');

-- Insert sample transfers
INSERT INTO transfers (from_account_id, to_account_id, recipient_name, amount, currency, description, status, created_by) VALUES
('650e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************', 'CONNECTING MATRIX LLC - Savings', 50000.00, 'USD', 'Transfer to savings account', 'completed', '123e4567-e89b-12d3-a456-************'),
('650e8400-e29b-41d4-a716-************', NULL, 'Tech Solutions Inc', 8500.00, 'USD', 'Payment for development services', 'completed', '123e4567-e89b-12d3-a456-************'),
('650e8400-e29b-41d4-a716-************', NULL, 'Digital Marketing Pro', 3200.00, 'USD', 'Monthly marketing retainer', 'processing', '123e4567-e89b-12d3-a456-************');
