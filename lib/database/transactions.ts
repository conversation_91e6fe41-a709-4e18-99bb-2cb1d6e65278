import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database.types'

type Transaction = Database['public']['Tables']['transactions']['Row']
type TransactionInsert = Database['public']['Tables']['transactions']['Insert']
type TransactionUpdate = Database['public']['Tables']['transactions']['Update']

export class TransactionService {
  private supabase = createClient()

  async getTransactions(): Promise<Transaction[]> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return data || []
  }

  async getTransactionById(id: string): Promise<Transaction | null> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Transaction not found
      }
      throw new Error(`Failed to fetch transaction: ${error.message}`)
    }

    return data
  }

  async getTransactionsByAccount(accountId: string): Promise<Transaction[]> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return data || []
  }

  async createTransaction(transaction: TransactionInsert): Promise<Transaction> {
    const { data, error } = await this.supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create transaction: ${error.message}`)
    }

    return data
  }

  async updateTransaction(id: string, updates: TransactionUpdate): Promise<Transaction> {
    const { data, error } = await this.supabase
      .from('transactions')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update transaction: ${error.message}`)
    }

    return data
  }

  async deleteTransaction(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('transactions')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete transaction: ${error.message}`)
    }
  }

  async getTransactionsByDateRange(
    accountId: string,
    startDate: string,
    endDate: string
  ): Promise<Transaction[]> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('account_id', accountId)
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return data || []
  }

  async getTransactionsByCategory(
    accountId: string,
    category: string
  ): Promise<Transaction[]> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('account_id', accountId)
      .eq('category', category)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return data || []
  }

  async getTransactionStats(accountId: string): Promise<{
    totalCredits: number
    totalDebits: number
    balance: number
    transactionCount: number
  }> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('type, amount')
      .eq('account_id', accountId)
      .eq('status', 'completed')

    if (error) {
      throw new Error(`Failed to fetch transaction stats: ${error.message}`)
    }

    const stats = {
      totalCredits: 0,
      totalDebits: 0,
      balance: 0,
      transactionCount: data.length
    }

    data.forEach(transaction => {
      if (transaction.type === 'credit') {
        stats.totalCredits += transaction.amount
      } else {
        stats.totalDebits += transaction.amount
      }
    })

    stats.balance = stats.totalCredits - stats.totalDebits

    return stats
  }
}
