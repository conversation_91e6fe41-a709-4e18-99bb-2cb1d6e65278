import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database.types'

type Organization = Database['public']['Tables']['organizations']['Row']
type OrganizationInsert = Database['public']['Tables']['organizations']['Insert']
type OrganizationUpdate = Database['public']['Tables']['organizations']['Update']

export class OrganizationService {
  private supabase = createClient()

  async getOrganizations(): Promise<Organization[]> {
    const { data, error } = await this.supabase
      .from('organizations')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch organizations: ${error.message}`)
    }

    return data || []
  }

  async getOrganizationById(id: string): Promise<Organization | null> {
    const { data, error } = await this.supabase
      .from('organizations')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Organization not found
      }
      throw new Error(`Failed to fetch organization: ${error.message}`)
    }

    return data
  }

  async createOrganization(organization: OrganizationInsert): Promise<Organization> {
    const { data, error } = await this.supabase
      .from('organizations')
      .insert(organization)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create organization: ${error.message}`)
    }

    return data
  }

  async updateOrganization(id: string, updates: OrganizationUpdate): Promise<Organization> {
    const { data, error } = await this.supabase
      .from('organizations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update organization: ${error.message}`)
    }

    return data
  }

  async deleteOrganization(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('organizations')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete organization: ${error.message}`)
    }
  }
}
