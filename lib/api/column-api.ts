/**
 * Client-side API service for Column operations
 * This service makes calls to our Next.js API routes, not directly to Column API
 */

export interface ColumnHealthResponse {
    configured: boolean;
    healthy: boolean;
    environment: string;
    sandbox?: boolean;
    message?: string;
    error?: string;
}

export interface ColumnEntity {
    id: string;
    type: string;  // Column API returns "BUSINESS" or "PERSON" in uppercase
    name?: string;  // Column API uses 'name' field
    legal_name?: string;
    dba_name?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    verification_status: string;  // Column API uses this instead of 'status'
    is_root: boolean;
    documents: any[];
    review_reasons: any[];
    created_at?: string;
}

export interface ColumnListResponse<T> {
    entities?: T[];  // Column API uses 'entities' field
    data?: T[];      // Keep for compatibility with other endpoints
    has_more: boolean;
    next_cursor?: string;
}

export interface OrganizationOption {
    label: string;
    value: string;
}

class ColumnApiService {
    private baseUrl = '/api/column';

    /**
     * Check Column API health and configuration
     */
    async checkHealth(): Promise<ColumnHealthResponse> {
        const response = await fetch(`${this.baseUrl}/health`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to check Column health');
        }

        return data;
    }

    /**
     * Get organizations for dropdown (business entities)
     */
    async getOrganizations(): Promise<OrganizationOption[]> {
        try {
            const response = await fetch(`${this.baseUrl}/entities?type=business&limit=50`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to fetch organizations');
            }

            if (data.entities && data.entities.length > 0) {
                return data.entities.map((entity: ColumnEntity) => ({
                    label: entity.name || entity.dba_name || `Business ${entity.id.slice(-8)}`,
                    value: entity.id
                }));
            }
        } catch (error) {
            console.error('Failed to fetch organizations:', error);
        }
        return [
            { label: 'No Organizations Found', value: 'no-orgs' },
            // { label: 'Create Business Entity', value: 'create-business' }
        ];
    }

    /**
     * List entities
     */
    async listEntities(params?: { limit?: number; cursor?: string; type?: 'person' | 'business'; status?: string }): Promise<ColumnListResponse<ColumnEntity>> {
        const searchParams = new URLSearchParams();

        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.cursor) searchParams.append('cursor', params.cursor);
        if (params?.type) searchParams.append('type', params.type);
        if (params?.status) searchParams.append('status', params.status);

        const response = await fetch(`${this.baseUrl}/entities?${searchParams}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to list entities');
        }

        return data;
    }

    /**
     * Create a test entity
     */
    async createTestEntity(type: 'person' | 'business' = 'person'): Promise<ColumnEntity> {
        const response = await fetch(`${this.baseUrl}/entities`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ type })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to create entity');
        }

        return data;
    }

    /**
     * List bank accounts
     */
    async listBankAccounts(params?: { limit?: number; cursor?: string; entity_id?: string; status?: string }): Promise<ColumnListResponse<any>> {
        const searchParams = new URLSearchParams();

        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.cursor) searchParams.append('cursor', params.cursor);
        if (params?.entity_id) searchParams.append('entity_id', params.entity_id);
        if (params?.status) searchParams.append('status', params.status);

        const response = await fetch(`${this.baseUrl}/accounts?${searchParams}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to list bank accounts');
        }

        return data;
    }

    /**
     * List transfers
     */
    async listTransfers(params?: { limit?: number; cursor?: string; account_id?: string; type?: string; status?: string; start_date?: string; end_date?: string }): Promise<ColumnListResponse<any>> {
        const searchParams = new URLSearchParams();

        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.cursor) searchParams.append('cursor', params.cursor);
        if (params?.account_id) searchParams.append('account_id', params.account_id);
        if (params?.type) searchParams.append('type', params.type);
        if (params?.status) searchParams.append('status', params.status);
        if (params?.start_date) searchParams.append('start_date', params.start_date);
        if (params?.end_date) searchParams.append('end_date', params.end_date);

        const response = await fetch(`${this.baseUrl}/transfers?${searchParams}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to list transfers');
        }

        return data;
    }
}

// Export singleton instance
export const columnApi = new ColumnApiService();
