# Column Banking Integration Setup Guide

This guide will help you integrate Column's banking-as-a-service platform with your application. Column is a nationally chartered bank that provides real banking infrastructure through developer-friendly APIs.

## Overview

Column provides:
- **Real Bank Accounts**: FDIC-insured bank accounts
- **ACH Transfers**: Automated Clearing House payments
- **Wire Transfers**: Domestic and international wire transfers
- **Check Processing**: Issue, deposit, and manage checks
- **Real-time Payments**: FedNow instant payments
- **Webhooks**: Real-time event notifications

## Step 1: Create Column Account

### Sign Up for Column
1. Go to [Column Dashboard](https://dashboard.column.com/register)
2. Create your developer account
3. Complete the onboarding process
4. Verify your identity and business information

### Account Verification
Column requires business verification for production access:
- Business registration documents
- Tax identification numbers
- Beneficial ownership information
- Compliance documentation

## Step 2: Get API Credentials

### Access API Keys
1. Log into [Column Dashboard](https://dashboard.column.com/login)
2. Navigate to **API Keys** section
3. Generate your API keys:
   - **Sandbox Key**: `test_xxxxxxxxxxxxxxxx` (for development)
   - **Production Key**: `live_xxxxxxxxxxxxxxxx` (for live transactions)

### API Authentication
Column uses HTTP Basic Authentication:
- **Username**: Leave blank
- **Password**: Your API key
- **Format**: `Authorization: Basic :<API_KEY>`

## Step 3: Environment Configuration

### Add Column Environment Variables
Add these to your `.env.local` file:

```env
# Column API Configuration
COLUMN_API_KEY=test_xxxxxxxxxxxxxxxx
COLUMN_BASE_URL=https://api.column.com
COLUMN_ENVIRONMENT=sandbox

# Production Configuration (when ready)
# COLUMN_API_KEY=live_xxxxxxxxxxxxxxxx
# COLUMN_ENVIRONMENT=production

# Webhook Configuration
COLUMN_WEBHOOK_SECRET=your_webhook_secret_here
COLUMN_WEBHOOK_URL=https://yourdomain.com/api/webhooks/column
```

### Update Environment Validation
Add Column variables to `scripts/validate-env.js`:

```javascript
const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'COLUMN_API_KEY'  // Add this line
];

const optionalVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'DATABASE_URL',
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_APP_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'COLUMN_BASE_URL',        // Add these lines
    'COLUMN_ENVIRONMENT',
    'COLUMN_WEBHOOK_SECRET',
    'COLUMN_WEBHOOK_URL'
];
```

## Step 4: Install Dependencies

```bash
# Install HTTP client for API calls
yarn add axios

# Install webhook signature verification
yarn add crypto

# Install date utilities for financial data
yarn add date-fns

# Install validation library
yarn add zod
```

## Step 5: Create Column Service

### Base Column Client
Create `lib/column/column-client.ts`:

```typescript
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

export class ColumnClient {
    private client: AxiosInstance;
    private apiKey: string;
    private baseURL: string;

    constructor() {
        this.apiKey = process.env.COLUMN_API_KEY!;
        this.baseURL = process.env.COLUMN_BASE_URL || 'https://api.column.com';
        
        if (!this.apiKey) {
            throw new Error('COLUMN_API_KEY environment variable is required');
        }

        this.client = axios.create({
            baseURL: this.baseURL,
            auth: {
                username: '',
                password: this.apiKey
            },
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: 30000
        });

        // Request interceptor for logging
        this.client.interceptors.request.use((config) => {
            console.log(`Column API Request: ${config.method?.toUpperCase()} ${config.url}`);
            return config;
        });

        // Response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                console.error('Column API Error:', error.response?.data || error.message);
                throw error;
            }
        );
    }

    async get(endpoint: string, config?: AxiosRequestConfig) {
        const response = await this.client.get(endpoint, config);
        return response.data;
    }

    async post(endpoint: string, data?: any, config?: AxiosRequestConfig) {
        const response = await this.client.post(endpoint, data, config);
        return response.data;
    }

    async put(endpoint: string, data?: any, config?: AxiosRequestConfig) {
        const response = await this.client.put(endpoint, data, config);
        return response.data;
    }

    async delete(endpoint: string, config?: AxiosRequestConfig) {
        const response = await this.client.delete(endpoint, config);
        return response.data;
    }
}

export const columnClient = new ColumnClient();
```

## Step 6: Create Type Definitions

### Column Types
Create `types/column.types.ts`:

```typescript
// Entity Types
export interface ColumnEntity {
    id: string;
    type: 'person' | 'business';
    status: 'pending' | 'approved' | 'rejected';
    created_at: string;
    updated_at: string;
}

export interface PersonEntity extends ColumnEntity {
    type: 'person';
    first_name: string;
    last_name: string;
    date_of_birth: string;
    ssn: string;
    phone: string;
    email: string;
    address: Address;
}

export interface BusinessEntity extends ColumnEntity {
    type: 'business';
    legal_name: string;
    dba_name?: string;
    ein: string;
    phone: string;
    email: string;
    address: Address;
    business_type: string;
    industry: string;
}

export interface Address {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
}

// Bank Account Types
export interface BankAccount {
    id: string;
    entity_id: string;
    account_type: 'checking' | 'savings';
    status: 'pending' | 'active' | 'closed';
    balance: number;
    available_balance: number;
    currency: string;
    created_at: string;
    updated_at: string;
}

export interface AccountNumber {
    id: string;
    bank_account_id: string;
    account_number: string;
    routing_number: string;
    status: 'active' | 'inactive';
}

// Transfer Types
export interface Transfer {
    id: string;
    type: 'ach' | 'wire' | 'book' | 'check' | 'realtime';
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    amount: number;
    currency: string;
    description: string;
    from_account_id: string;
    to_account_id?: string;
    counterparty_id?: string;
    created_at: string;
    updated_at: string;
}

export interface ACHTransfer extends Transfer {
    type: 'ach';
    ach_type: 'credit' | 'debit';
    effective_date: string;
    same_day: boolean;
}

export interface WireTransfer extends Transfer {
    type: 'wire';
    wire_type: 'domestic' | 'international';
    beneficiary_name: string;
    beneficiary_address: Address;
    intermediary_bank?: BankInfo;
    beneficiary_bank: BankInfo;
}

export interface BankInfo {
    name: string;
    routing_number?: string;
    swift_code?: string;
    address: Address;
}

// Counterparty Types
export interface Counterparty {
    id: string;
    name: string;
    account_number: string;
    routing_number: string;
    account_type: 'checking' | 'savings';
    bank_name: string;
    created_at: string;
}

// Event Types
export interface ColumnEvent {
    id: string;
    type: string;
    data: any;
    created_at: string;
    processed_at?: string;
}

// API Response Types
export interface ColumnResponse<T> {
    data: T;
    has_more?: boolean;
    next_cursor?: string;
}

export interface ColumnError {
    error: {
        type: string;
        code: string;
        message: string;
        details?: any;
    };
}
```

This setup provides the foundation for Column integration. The next steps would include creating specific services for entities, bank accounts, transfers, and webhooks.

## Next Steps

1. **Create Entity Service**: Manage person and business entities
2. **Create Bank Account Service**: Handle account creation and management
3. **Create Transfer Service**: Process ACH, wire, and other transfers
4. **Set Up Webhooks**: Handle real-time event notifications
5. **Create UI Components**: Build banking interface components
6. **Implement Security**: Add proper error handling and validation

## Important Notes

### Compliance Requirements
- Column requires KYC (Know Your Customer) verification
- Business entities need additional documentation
- Production access requires compliance review

### Rate Limits
- Sandbox: 1000 requests per minute
- Production: Higher limits available upon request

### Testing
- Use sandbox environment for development
- Column provides simulation endpoints for testing
- Test all transfer types before production

### Security
- Never expose API keys in client-side code
- Use environment variables for all credentials
- Implement proper error handling
- Log all API interactions for audit trails

## Step 7: Example Usage

### Basic Entity and Account Creation

```typescript
import { columnService } from '@/lib/column/column-service';

// Create a person entity
const person = await columnService.createPersonEntity({
    first_name: 'John',
    last_name: 'Doe',
    date_of_birth: '1990-01-01',
    ssn: '*********',
    phone: '+**********',
    email: '<EMAIL>',
    address: {
        line1: '123 Main St',
        city: 'San Francisco',
        state: 'CA',
        postal_code: '94105',
        country: 'US'
    }
});

// Create a bank account for the entity
const account = await columnService.createBankAccount({
    entity_id: person.id,
    account_type: 'checking',
    nickname: 'Primary Checking'
});

// Create account numbers
const accountNumber = await columnService.createAccountNumber(account.id);
```

### Transfer Operations

```typescript
// Create a counterparty
const counterparty = await columnService.createCounterparty({
    name: 'Jane Smith',
    account_number: '**********',
    routing_number: '*********',
    account_type: 'checking',
    bank_name: 'Chase Bank'
});

// Send an ACH transfer
const transfer = await columnService.createACHTransfer({
    from_account_id: account.id,
    counterparty_id: counterparty.id,
    amount: 10000, // $100.00 in cents
    description: 'Payment for services',
    ach_type: 'credit',
    same_day: false
});
```

### Webhook Setup

```typescript
import { columnWebhookService } from '@/lib/column/webhook-service';

// Create a webhook endpoint
const webhook = await columnWebhookService.createWebhook({
    url: 'https://yourdomain.com/api/webhooks/column',
    events: [
        'transfer.completed',
        'transfer.failed',
        'entity.approved',
        'bank_account.created'
    ]
});
```

## Step 8: Testing in Sandbox

### Simulation Examples

```typescript
// Simulate receiving money (sandbox only)
const incomingTransfer = await columnService.simulateACHCredit({
    account_id: account.id,
    amount: 50000, // $500.00
    counterparty_name: 'Test Company',
    counterparty_account_number: '**********',
    counterparty_routing_number: '*********',
    description: 'Test payment'
});

// Simulate transfer settlement
await columnService.simulateACHSettle(transfer.id);
```

## Step 9: Production Checklist

### Before Going Live
- [ ] Complete Column's compliance review
- [ ] Update API key to production (`live_` prefix)
- [ ] Set `COLUMN_ENVIRONMENT=production`
- [ ] Configure production webhook URLs
- [ ] Test all critical flows
- [ ] Set up monitoring and alerting
- [ ] Review and test error handling
- [ ] Implement proper logging

### Security Best Practices
- [ ] Never expose API keys in client-side code
- [ ] Use HTTPS for all webhook endpoints
- [ ] Verify webhook signatures
- [ ] Implement rate limiting
- [ ] Log all API interactions
- [ ] Set up proper error monitoring

For detailed API documentation, visit: https://column.com/docs
