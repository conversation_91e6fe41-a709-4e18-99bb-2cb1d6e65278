# Supabase Setup Guide for equate

This guide will help you set up Supa<PERSON> as the backend for your banking application.

## Prerequisites

- Node.js 18+ installed
- Yarn package manager
- A Supabase account (free tier available)

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `developer-infrastructure-bank`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be ready (2-3 minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Project API Keys** → `anon` `public` key
   - **Project API Keys** → `service_role` `secret` key (keep this secure!)

## Step 3: Configure Environment Variables

1. Update your `.env.local` file with your Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database Configuration
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres

# App Configuration
NEXT_PUBLIC_APP_NAME="equate"
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Security
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000
```

## Step 4: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `lib/database/schema.sql`
3. Paste it into the SQL Editor and click **Run**
4. This will create all necessary tables, indexes, and security policies

## Step 5: Seed Sample Data (Optional)

1. In the SQL Editor, copy the contents of `lib/database/seed.sql`
2. Paste and run to insert sample data
3. This creates sample organizations, users, accounts, and transactions

## Step 6: Configure Authentication

1. Go to **Authentication** → **Settings**
2. Configure the following:

### Site URL
- Add your domain: `http://localhost:3000` (development)
- For production, add your actual domain

### Redirect URLs
Add these URLs for auth redirects:
- `http://localhost:3000/callback`
- `http://localhost:3000/reset-password`
- `http://localhost:3000/accept-invite`

### Disable Email Confirmation (Development)
For development purposes, you can disable email confirmation:

1. Go to **Authentication** → **Settings**
2. Scroll down to **User Signups**
3. **Uncheck** "Enable email confirmations"
4. Click **Save**

**Note**: This allows users to sign in immediately without email verification. For production, you should enable email confirmation for security.

### Email Templates (Optional)
Customize the email templates for:
- Confirm signup
- Invite user
- Reset password

## Step 7: Set Up Row Level Security (RLS)

The schema already includes basic RLS policies, but you may want to customize them:

1. Go to **Authentication** → **Policies**
2. Review and modify policies as needed for your business logic
3. Test policies with different user roles

## Step 8: Test the Integration

1. Start your development server:
```bash
yarn dev
```

2. Navigate to `http://localhost:3000`
3. Try signing up/logging in
4. Check that data is being saved to Supabase

## Database Schema Overview

### Core Tables

- **profiles**: User profiles extending Supabase auth
- **organizations**: Company/entity information
- **accounts**: Bank accounts for organizations
- **transactions**: Financial transactions
- **transfers**: Money transfers between accounts
- **contacts**: Recipient contacts for transfers

### Key Features

- **UUID primary keys** for all tables
- **Row Level Security** enabled on all tables
- **Automatic timestamps** with triggers
- **Proper foreign key relationships**
- **Indexed columns** for performance
- **Type safety** with TypeScript

## Security Considerations

### Environment Variables
- Never commit `.env.local` to version control
- Use different keys for development/staging/production
- Rotate keys regularly

### Row Level Security
- All tables have RLS enabled
- Policies restrict data access based on user authentication
- Test policies thoroughly before production

### API Keys
- **Anon key**: Safe to use in client-side code
- **Service role key**: Server-side only, has admin privileges

## Monitoring and Maintenance

### Supabase Dashboard
Monitor your application through:
- **Database**: View tables, run queries
- **Auth**: Manage users and sessions
- **Storage**: File uploads (if needed)
- **Edge Functions**: Serverless functions
- **Logs**: Debug issues

### Backup Strategy
- Supabase automatically backs up your database
- For critical applications, consider additional backup strategies
- Export data regularly for compliance

## Troubleshooting

### Common Issues

1. **Connection errors**: Check environment variables
2. **Auth issues**: Verify redirect URLs and site URL
3. **Permission errors**: Review RLS policies
4. **Type errors**: Regenerate types if schema changes

### Generating Types
To update TypeScript types after schema changes:

```bash
npx supabase gen types typescript --project-id your-project-id > types/database.types.ts
```

## Production Deployment

### Environment Setup
1. Set production environment variables
2. Update site URL and redirect URLs
3. Configure custom domain (optional)
4. Set up monitoring and alerts

### Performance Optimization
1. Review and optimize database queries
2. Add indexes for frequently queried columns
3. Consider connection pooling for high traffic
4. Monitor query performance in dashboard

## Support

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

## Next Steps

1. Customize the database schema for your specific needs
2. Implement additional business logic in Edge Functions
3. Set up file storage for documents/receipts
4. Configure webhooks for external integrations
5. Implement audit logging for compliance
