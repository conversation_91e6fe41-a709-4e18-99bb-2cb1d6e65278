# equate - Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# ============================================================================
# REQUIRED - Supabase Configuration
# ============================================================================
# Get these from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# ============================================================================
# OPTIONAL - Column Banking API (for real banking features)
# ============================================================================
# Sign up at https://dashboard.column.com/register to get these
# Leave commented out if you don't want banking features yet
# COLUMN_API_KEY=test_xxxxxxxxxxxxxxxx
# COLUMN_BASE_URL=https://api.column.com
# COLUMN_ENVIRONMENT=sandbox
# COLUMN_WEBHOOK_SECRET=your-webhook-secret-here
# COLUMN_WEBHOOK_URL=https://yourdomain.com/api/webhooks/column

# ============================================================================
# OPTIONAL - Additional Configuration
# ============================================================================
# These are optional but recommended for full functionality

# Supabase Service Role (for server-side operations)
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database URL (for direct database access if needed)
# DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres

# App Configuration
NEXT_PUBLIC_APP_NAME="equate"
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Security (for NextAuth if using)
# NEXTAUTH_SECRET=your-nextauth-secret-here
# NEXTAUTH_URL=http://localhost:3000

# ============================================================================
# Setup Instructions
# ============================================================================
# 1. Copy this file to .env.local
# 2. Fill in your Supabase credentials (required)
# 3. Optionally add Column API key for banking features
# 4. Run: yarn validate-env
# 5. Run: yarn dev
#
# For detailed setup instructions:
# - Supabase: See SUPABASE_SETUP.md
# - Column: See COLUMN_SETUP.md
