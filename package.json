{"name": "equate", "version": "10.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "format": "prettier --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint", "validate-env": "node scripts/validate-env.js", "setup": "node scripts/validate-env.js && echo '✅ Setup complete! Run yarn dev to start the development server.'"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "axios": "^1.9.0", "bufferutil": "^4.0.9", "chart.js": "4.2.1", "date-fns": "^4.1.0", "next": "13.4.8", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "10.2.1", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.1.3", "utf-8-validate": "^6.0.5", "zod": "^3.25.63"}, "devDependencies": {"eslint": "8.43.0", "eslint-config-next": "13.4.6", "prettier": "^2.8.8", "sass": "^1.63.4"}}