# equate

A modern banking application built with Next.js, TypeScript, PrimeReact, and Supabase. This application provides a comprehensive banking interface for managing accounts, transactions, transfers, and user management.

## 🏦 Features

### Core Banking Features
- **Real Bank Accounts**: FDIC-insured accounts through Column's chartered bank
- **Multiple Transfer Types**: ACH, Wire, Real-time (FedNow), Check processing
- **Account Management**: Multiple account types (checking, savings, business)
- **Transaction History**: Complete transaction tracking with categories
- **Contact Management**: Manage transfer recipients and their banking details
- **Real-time Updates**: Live balance and transaction notifications via webhooks

### User Management
- **Role-based Access Control**: Owner, Admin, Employee, Finance roles
- **User Invitation System**: Invite users with specific roles
- **Profile Management**: User profiles with employment details
- **Authentication**: Secure login/signup with Supabase Auth

### Dashboard & Analytics
- **Financial Overview**: Account balances and transaction summaries
- **Recent Activity**: Latest transactions and transfers
- **User Statistics**: Active users and invitation status
- **Account Analytics**: Transaction statistics and trends

### Security & Compliance
- **Row Level Security**: Database-level access control
- **Audit Trails**: Complete transaction and user activity logging
- **Secure Authentication**: Email verification and password reset
- **Environment Configuration**: Secure API key management

## 🚀 Tech Stack

- **Frontend**: Next.js 13+ (App Router), TypeScript, React 18
- **UI Components**: PrimeReact, PrimeFlex, PrimeIcons
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Banking API**: Column (FDIC-insured accounts, ACH, Wire, Real-time transfers)
- **Styling**: SCSS, CSS Modules
- **Package Manager**: Yarn
- **Development**: ESLint, Prettier

## 📋 Prerequisites

- Node.js 18+ installed
- Yarn package manager
- A Supabase account (free tier available)

## 🛠️ Quick Setup

### ⚡ 5-Minute Setup (Development)

For immediate testing and development, follow our [Quick Setup Guide](./QUICK_SETUP.md):

```bash
# 1. Install dependencies
yarn install

# 2. Set up Supabase project and disable email confirmation
# 3. Create .env.local with your Supabase credentials
# 4. Run database schema
# 5. Validate and start

yarn validate-env
yarn dev
```

### 📖 Detailed Setup

For production setup or detailed configuration:

**Database Setup**: Follow [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)
1. **Create Supabase Project**: Complete backend setup
2. **Configure Authentication**: Email settings, redirects, security
3. **Set Up Database**: Schema, RLS policies, sample data
4. **Environment Variables**: Production-ready configuration

**Banking Integration**: Follow [COLUMN_SETUP.md](./COLUMN_SETUP.md)
1. **Create Column Account**: Banking API access
2. **API Configuration**: Keys, webhooks, environment setup
3. **Entity Management**: Person and business entities
4. **Account Creation**: Bank accounts and transfers
5. **Production Deployment**: Compliance and security

### 🔧 Environment Configuration

Create a `.env.local` file in the root directory:

```env
# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# Column Banking API (Required for banking features)
COLUMN_API_KEY=test_xxxxxxxxxxxxxxxx
COLUMN_ENVIRONMENT=sandbox

# App Configuration (Optional)
NEXT_PUBLIC_APP_NAME="equate"
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### ✅ Validate & Start

```bash
# Validate your setup
yarn validate-env

# Start development server
yarn dev
```

The application will be available at `http://localhost:3000`
