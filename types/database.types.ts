export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'owner' | 'admin' | 'employee' | 'finance_admin' | 'finance_manager' | 'bookkeeper' | 'developer'
          status: 'active' | 'suspended' | 'invited' | 'expired'
          employment_entity: string | null
          manager_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'owner' | 'admin' | 'employee' | 'finance_admin' | 'finance_manager' | 'bookkeeper' | 'developer'
          status?: 'active' | 'suspended' | 'invited' | 'expired'
          employment_entity?: string | null
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'owner' | 'admin' | 'employee' | 'finance_admin' | 'finance_manager' | 'bookkeeper' | 'developer'
          status?: 'active' | 'suspended' | 'invited' | 'expired'
          employment_entity?: string | null
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      organizations: {
        Row: {
          id: string
          name: string
          legal_entity_name: string
          entity_registration_location: string
          legal_entity_form: string
          registered_office_address: string
          ultimate_beneficial_owner: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          legal_entity_name: string
          entity_registration_location: string
          legal_entity_form: string
          registered_office_address: string
          ultimate_beneficial_owner: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          legal_entity_name?: string
          entity_registration_location?: string
          legal_entity_form?: string
          registered_office_address?: string
          ultimate_beneficial_owner?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      accounts: {
        Row: {
          id: string
          organization_id: string
          account_name: string
          account_type: 'checking' | 'savings' | 'business'
          currency: string
          balance: number
          status: 'active' | 'suspended' | 'closed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          account_name: string
          account_type: 'checking' | 'savings' | 'business'
          currency?: string
          balance?: number
          status?: 'active' | 'suspended' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          account_name?: string
          account_type?: 'checking' | 'savings' | 'business'
          currency?: string
          balance?: number
          status?: 'active' | 'suspended' | 'closed'
          created_at?: string
          updated_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          account_id: string
          type: 'credit' | 'debit'
          amount: number
          currency: string
          description: string
          category: string | null
          reference: string | null
          status: 'pending' | 'completed' | 'failed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          account_id: string
          type: 'credit' | 'debit'
          amount: number
          currency?: string
          description: string
          category?: string | null
          reference?: string | null
          status?: 'pending' | 'completed' | 'failed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          account_id?: string
          type?: 'credit' | 'debit'
          amount?: number
          currency?: string
          description?: string
          category?: string | null
          reference?: string | null
          status?: 'pending' | 'completed' | 'failed'
          created_at?: string
          updated_at?: string
        }
      }
      transfers: {
        Row: {
          id: string
          from_account_id: string
          to_account_id: string | null
          recipient_name: string | null
          recipient_email: string | null
          recipient_bank_details: Json | null
          amount: number
          currency: string
          description: string
          status: 'pending' | 'processing' | 'completed' | 'failed'
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          from_account_id: string
          to_account_id?: string | null
          recipient_name?: string | null
          recipient_email?: string | null
          recipient_bank_details?: Json | null
          amount: number
          currency?: string
          description: string
          status?: 'pending' | 'processing' | 'completed' | 'failed'
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          from_account_id?: string
          to_account_id?: string | null
          recipient_name?: string | null
          recipient_email?: string | null
          recipient_bank_details?: Json | null
          amount?: number
          currency?: string
          description?: string
          status?: 'pending' | 'processing' | 'completed' | 'failed'
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      contacts: {
        Row: {
          id: string
          organization_id: string
          name: string
          email: string | null
          phone: string | null
          bank_details: Json | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          email?: string | null
          phone?: string | null
          bank_details?: Json | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          email?: string | null
          phone?: string | null
          bank_details?: Json | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'owner' | 'admin' | 'employee' | 'finance_admin' | 'finance_manager' | 'bookkeeper' | 'developer'
      user_status: 'active' | 'suspended' | 'invited' | 'expired'
      account_type: 'checking' | 'savings' | 'business'
      account_status: 'active' | 'suspended' | 'closed'
      transaction_type: 'credit' | 'debit'
      transaction_status: 'pending' | 'completed' | 'failed'
      transfer_status: 'pending' | 'processing' | 'completed' | 'failed'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
