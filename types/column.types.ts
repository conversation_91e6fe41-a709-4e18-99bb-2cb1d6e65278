// Base types
export interface Address {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
}

export interface BankInfo {
    name: string;
    routing_number?: string;
    swift_code?: string;
    address: Address;
}

// Entity Types
export interface ColumnEntity {
    id: string;
    type: 'person' | 'business';
    status: 'pending' | 'approved' | 'rejected' | 'requires_review';
    created_at: string;
    updated_at: string;
}

export interface PersonEntity extends ColumnEntity {
    type: 'person';
    first_name: string;
    last_name: string;
    date_of_birth: string;
    ssn: string;
    phone: string;
    email: string;
    address: Address;
    identity_verification_status?: 'pending' | 'verified' | 'failed';
}

export interface BusinessEntity extends ColumnEntity {
    type: 'business';
    legal_name: string;
    dba_name?: string;
    ein: string;
    phone: string;
    email: string;
    address: Address;
    business_type: 'corporation' | 'llc' | 'partnership' | 'sole_proprietorship';
    industry: string;
    formation_date: string;
    formation_state: string;
}

// Bank Account Types
export interface BankAccount {
    id: string;
    entity_id: string;
    account_type: 'checking' | 'savings';
    status: 'pending' | 'active' | 'closed' | 'frozen';
    balance: number;
    available_balance: number;
    currency: string;
    nickname?: string;
    is_overdraftable: boolean;
    overdraft_limit?: number;
    created_at: string;
    updated_at: string;
}

export interface AccountNumber {
    id: string;
    bank_account_id: string;
    account_number: string;
    routing_number: string;
    status: 'active' | 'inactive';
    created_at: string;
}

export interface BankAccountSummary {
    bank_account_id: string;
    date: string;
    opening_balance: number;
    closing_balance: number;
    total_credits: number;
    total_debits: number;
    transaction_count: number;
}

// Transfer Types
export interface Transfer {
    id: string;
    type: 'ach' | 'wire' | 'book' | 'check' | 'realtime' | 'international_wire';
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'returned';
    amount: number;
    currency: string;
    description: string;
    from_account_id: string;
    to_account_id?: string;
    counterparty_id?: string;
    reference_id?: string;
    created_at: string;
    updated_at: string;
    settled_at?: string;
}

export interface ACHTransfer extends Transfer {
    type: 'ach';
    ach_type: 'credit' | 'debit';
    effective_date: string;
    same_day: boolean;
    sec_code: 'PPD' | 'CCD' | 'WEB' | 'TEL';
    return_reason?: string;
}

export interface WireTransfer extends Transfer {
    type: 'wire';
    wire_type: 'domestic' | 'international';
    beneficiary_name: string;
    beneficiary_address: Address;
    beneficiary_bank: BankInfo;
    intermediary_bank?: BankInfo;
    purpose_code?: string;
    regulatory_reporting?: any;
}

export interface BookTransfer extends Transfer {
    type: 'book';
    // Book transfers are internal transfers between Column accounts
}

export interface CheckTransfer extends Transfer {
    type: 'check';
    check_number?: string;
    memo?: string;
    mail_to?: Address;
    print_date?: string;
    mail_date?: string;
}

export interface RealtimeTransfer extends Transfer {
    type: 'realtime';
    // FedNow real-time payments
    end_to_end_id: string;
    remittance_information?: string;
}

// Counterparty Types
export interface Counterparty {
    id: string;
    name: string;
    account_number: string;
    routing_number: string;
    account_type: 'checking' | 'savings';
    bank_name: string;
    nickname?: string;
    is_verified: boolean;
    created_at: string;
    updated_at: string;
}

// Document Types
export interface Document {
    id: string;
    entity_id: string;
    type: 'identity' | 'address_verification' | 'business_registration' | 'tax_document' | 'other';
    filename: string;
    content_type: string;
    size: number;
    status: 'pending' | 'approved' | 'rejected';
    created_at: string;
    updated_at: string;
}

// Event Types
export interface ColumnEvent {
    id: string;
    type: string;
    data: any;
    created_at: string;
    processed_at?: string;
    webhook_delivered_at?: string;
}

// Webhook Types
export interface Webhook {
    id: string;
    url: string;
    events: string[];
    status: 'active' | 'inactive';
    secret: string;
    created_at: string;
    updated_at: string;
}

export interface WebhookDelivery {
    id: string;
    webhook_id: string;
    event_id: string;
    status: 'pending' | 'delivered' | 'failed';
    response_status?: number;
    response_body?: string;
    attempts: number;
    next_attempt_at?: string;
    created_at: string;
    delivered_at?: string;
}

// API Response Types
export interface ColumnResponse<T> {
    data: T;
    has_more?: boolean;
    next_cursor?: string;
}

export interface ColumnListResponse<T> {
    entities?: T[];  // Column API uses 'entities' field for entity endpoints
    data?: T[];      // Some endpoints might use 'data' field
    has_more: boolean;
    next_cursor?: string;
}

export interface ColumnError {
    error: {
        type: string;
        code: string;
        message: string;
        details?: any;
        param?: string;
    };
}

// Request Types for Creating Entities
export interface CreatePersonEntityRequest {
    first_name: string;
    last_name: string;
    date_of_birth: string; // YYYY-MM-DD format
    ssn: string;
    phone: string;
    email: string;
    address: Address;
}

export interface CreateBusinessEntityRequest {
    legal_name: string;
    dba_name?: string;
    ein: string;
    phone: string;
    email: string;
    address: Address;
    business_type: 'corporation' | 'llc' | 'partnership' | 'sole_proprietorship';
    industry: string;
    formation_date: string; // YYYY-MM-DD format
    formation_state: string;
}

// Request Types for Creating Bank Accounts
export interface CreateBankAccountRequest {
    entity_id: string;
    account_type: 'checking' | 'savings';
    nickname?: string;
    is_overdraftable?: boolean;
    overdraft_limit?: number;
}

// Request Types for Creating Transfers
export interface CreateACHTransferRequest {
    from_account_id: string;
    to_account_id?: string;
    counterparty_id?: string;
    amount: number;
    description: string;
    ach_type: 'credit' | 'debit';
    effective_date?: string;
    same_day?: boolean;
    sec_code?: 'PPD' | 'CCD' | 'WEB' | 'TEL';
}

export interface CreateWireTransferRequest {
    from_account_id: string;
    amount: number;
    description: string;
    beneficiary_name: string;
    beneficiary_address: Address;
    beneficiary_bank: BankInfo;
    intermediary_bank?: BankInfo;
    purpose_code?: string;
}

export interface CreateCounterpartyRequest {
    name: string;
    account_number: string;
    routing_number: string;
    account_type: 'checking' | 'savings';
    bank_name: string;
    nickname?: string;
}

// Simulation Types (for sandbox testing)
export interface SimulationRequest {
    account_id: string;
    amount: number;
    description?: string;
}

export interface ACHSimulationRequest extends SimulationRequest {
    counterparty_name: string;
    counterparty_account_number: string;
    counterparty_routing_number: string;
}

// Financial Institution Types
export interface FinancialInstitution {
    routing_number: string;
    name: string;
    address: Address;
    phone?: string;
    status: 'active' | 'inactive';
}

// Rate Limit Types
export interface RateLimitInfo {
    limit: number;
    remaining: number;
    reset_time: Date;
}
