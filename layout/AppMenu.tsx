/* eslint-disable @next/next/no-img-element */

import React, { useState, useEffect, useRef } from 'react';
import AppMenuitem from './AppMenuitem';
import { MenuProvider } from './context/menucontext';
import { AppMenuItem } from '@/types';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { columnApi } from '@/lib/api/column-api';
import { Toast } from 'primereact/toast';

const AppMenu = () => {
    const toast = useRef<Toast>(null);

    const [selectedOrg, setSelectedOrg] = useState('');
                
    const [organizations, setOrganizations] = useState<{ label: string; value: string }[]>([
        { label: 'CONNECTING MATRIX LLC', value: 'default-1' },
        { label: 'HELLO CORP', value: 'default-2' },
        { label: 'FALLOUT', value: 'default-3' }
    ]);
    const [loading, setLoading] = useState(true);

    // Fetch organizations from Column API
    // const fetchOrganizations = async () => {
    //     try {
    //         const orgOptions = await columnApi.getOrganizations();
    //         setOrganizations(orgOptions);

    //         // Set first organization as default if available
    //         if (orgOptions.length > 0) {
    //             setSelectedOrg(orgOptions[0].value);
    //         }
    //     } catch (error) {
    //         console.error('Failed to fetch organizations:', error);
    //         toast.current?.show({ severity: 'error', summary: 'Error', detail: 'Failed to fetch organizations', life: 3000 });
    //         // Fallback to default organizations
    //         // const fallbackOrgs = [
    //         //     { label: 'CONNECTING MATRIX LLC', value: 'default-1' },
    //         //     { label: 'HELLO CORP', value: 'default-2' },
    //         //     { label: 'FALLOUT', value: 'default-3' }
    //         // ];
    //         // setOrganizations(fallbackOrgs);
    //         // setSelectedOrg(fallbackOrgs[0].value);
    //     } finally {
    //         setLoading(false);
    //     }
    // };

    // useEffect(() => {
    //     fetchOrganizations();
    // }, []);

    const refreshOrganizations = () => {
        // setLoading(true);
        // fetchOrganizations();
    };

    const model: AppMenuItem[] = [
        {
            label: 'ORGANISATION',
            items: [
                { label: 'Dashboard', icon: 'pi pi-fw pi-home', to: '/' },
                { label: 'Expenses', icon: 'pi pi-fw pi-dollar', to: '/expenses' },
                { label: 'Bills', icon: 'pi pi-fw pi-file-o', to: '/bills' },
                { label: 'Requests', icon: 'pi pi-fw pi-send', to: '/requests' },
                { label: 'Reports', icon: 'pi pi-fw pi-chart-bar', to: '/reports' },
                {
                    label: 'Settings',
                    icon: 'pi pi-fw pi-cog',
                    to: '/settings',
                    items: [
                        { label: 'Organisation', icon: 'pi pi-fw pi-building', to: '/settings' },
                        { label: 'User Management', icon: 'pi pi-fw pi-users', to: '/user-management' }
                        // { label: 'Spend Approvals', icon: 'pi pi-fw pi-check-circle', to: '/pages/settings/approvals' },
                        // { label: 'Connections', icon: 'pi pi-fw pi-link', to: '/pages/settings/connections' }
                    ]
                }
            ]
        },
        {
            label: `ACCOUNT - ${organizations.find((org) => org.value === selectedOrg)?.label || 'Loading...'}`,
            items: [
                { label: 'Wallet', icon: 'pi pi-fw pi-wallet', to: '/wallet' },
                // {
                //     label: 'Transfers',
                //     icon: 'pi pi-fw pi-arrow-right-arrow-left',
                //     to: '/transfers',
                //     items: [
                //         { label: 'Contacts', icon: 'pi pi-fw pi-users', to: '/transfers/contacts' }
                //     ]
                // },
                { label: 'Transfers', icon: 'pi pi-fw pi-arrow-right-arrow-left', to: '/transfers' },
                { label: 'Contacts', icon: 'pi pi-fw pi-users', to: '/contacts' },
                { label: 'Cards', icon: 'pi pi-fw pi-credit-card', to: '/cards' },
                { label: 'Payments', icon: 'pi pi-fw pi-shopping-cart', to: '/payments' },
                // { label: 'Rewards', icon: 'pi pi-fw pi-star', to: '/rewards' },
                // { label: 'Developer', icon: 'pi pi-fw pi-code', to: '/developer' },
                { label: 'Security', icon: 'pi pi-fw pi-shield', to: '/security' },
                { label: 'Statements', icon: 'pi pi-fw pi-file-pdf', to: '/statements' }
            ]
        },
        {
            label: 'ADVANCED',
            items: [
                // { label: 'Onboarding Flow', icon: 'pi pi-fw pi-user-plus', to: '/onboarding' },
                { label: 'Account Activation', icon: 'pi pi-fw pi-verified', to: '/activation' },
                // { label: 'Column API Test', icon: 'pi pi-fw pi-cog', to: '/column-test' }
            ]
        }
    ];

    return (
        <MenuProvider>
            <Toast ref={toast} />
            <ul className="layout-menu">
                {/* Organization Selector */}
                <li className="layout-root-menuitem">
                    <div className="pt-3 pb-1 surface-border">
                        <div className="flex align-items-center gap-2 mb-2">
                            {/* <div className="w-2rem h-2rem bg-orange-500 border-circle flex align-items-center justify-content-center">
                                <span className="text-white font-bold text-sm">C</span>
                            </div> */}
                            <div className="flex-1">
                                <Dropdown value={selectedOrg} options={organizations} onChange={(e) => setSelectedOrg(e.value)} className="w-full" placeholder={loading ? 'Loading organizations...' : 'Select Organization'} disabled={loading} />
                            </div>
                            <Button icon="pi pi-refresh" className="p-button-outlined p-button-sm" onClick={refreshOrganizations} loading={loading} tooltip="Refresh Organizations" tooltipOptions={{ position: 'top' }} />
                        </div>
                        {/* <div className="text-xs text-500">Organisation</div> */}
                    </div>
                </li>

                {model.map((item, i) => {
                    return !item?.seperator ? <AppMenuitem item={item} root={true} index={i} key={item.label} /> : <li className="menu-separator"></li>;
                })}

                {/* <div className="mt-4 p-3 text-center">
                    <div className="flex align-items-center justify-content-center gap-2">
                        <i className="pi pi-bars text-xl"></i>
                        <span className="font-bold text-lg">Airwallex</span>
                    </div>
                </div> */}
            </ul>
        </MenuProvider>
    );
};

export default AppMenu;
