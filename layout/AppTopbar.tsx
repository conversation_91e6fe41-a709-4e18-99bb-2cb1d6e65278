/* eslint-disable @next/next/no-img-element */

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { OverlayPanel } from 'primereact/overlaypanel';
import { Button } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Divider } from 'primereact/divider';
import React, { forwardRef, useContext, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { AppTopbarRef } from '@/types';
import { LayoutContext } from './context/layoutcontext';
import { AuthService } from '@/lib/auth/auth-service';
import { classNames } from 'primereact/utils';

const AppTopbar = forwardRef<AppTopbarRef>((_props, ref) => {
    const { layoutState, onMenuToggle, showProfileSidebar } = useContext(LayoutContext);
    const router = useRouter();
    const authService = new AuthService();

    const menubuttonRef = useRef(null);
    const topbarmenuRef = useRef(null);
    const topbarmenubuttonRef = useRef(null);
    const overlayPanelRef = useRef<OverlayPanel>(null);

    const handleLogout = async () => {
        try {
            await authService.signOut();
            router.push('/login');
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    // const toggleUserMenu = (event: React.MouseEvent) => {
    //     overlayPanelRef.current?.toggle(event);
    // };

    useImperativeHandle(ref, () => ({
        menubutton: menubuttonRef.current,
        topbarmenu: topbarmenuRef.current,
        topbarmenubutton: topbarmenubuttonRef.current
    }));

    return (
        <div className="layout-topbar">
            <Link href="/" className="layout-topbar-logo text-primary font-bold">
                <span className="hidden lg:inline">equate</span>
                <span className="lg:hidden">equate</span>
            </Link>

            <button ref={menubuttonRef} type="button" className="p-link layout-menu-button layout-topbar-button" onClick={onMenuToggle}>
                <i className="pi pi-bars" />
            </button>

            <button ref={topbarmenubuttonRef} type="button" className="p-link layout-topbar-menu-button layout-topbar-button" onClick={showProfileSidebar}>
                <i className="pi pi-ellipsis-v" />
            </button>

            <div ref={topbarmenuRef} className={classNames('layout-topbar-menu', { 'layout-topbar-menu-mobile-active': layoutState.profileSidebarVisible })}>
                {/* <button type="button" className="p-link layout-topbar-button">
                    <i className="pi pi-calendar"></i>
                    <span>Calendar</span>
                </button> */}
                <button type="button" className="p-link layout-topbar-button" onClick={handleLogout}>
                <i className="pi pi-sign-out"></i>
                <span>Logout</span>
                </button>
                {/* <Link href="/documentation">
                    <button type="button" className="p-link layout-topbar-button">
                        <i className="pi pi-cog"></i>
                        <span>Settings</span>
                    </button>
                </Link> */}
            </div>
            
            {/* <div className="layout-topbar-menu">

                <button type="button" className="p-link layout-topbar-button" onClick={handleLogout}>
                    <i className="pi pi-sign-out text-red-600 font-bold"></i>
                    <span>Logout</span>
                </button>
            </div> */}
        </div>
    );
});

AppTopbar.displayName = 'AppTopbar';

export default AppTopbar;
