# Quick Setup Guide - equate

This guide will get you up and running in 5 minutes for development.

## 🚀 Quick Start (Development Mode)

### 1. Install Dependencies
```bash
yarn install
```

### 2. Set Up Supabase Project
1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be ready (2-3 minutes)
3. Go to **Settings** → **API** and copy your credentials

### 3. Create Environment File
Create `.env.local` in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# App Configuration
NEXT_PUBLIC_APP_NAME="equate"
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Disable Email Confirmation (Development Only)
**IMPORTANT**: For immediate testing without email verification:

1. In your Supabase dashboard, go to **Authentication** → **Settings**
2. Scroll down to **User Signups**
3. **Uncheck** "Enable email confirmations"
4. Click **Save**

### 5. Set Up Database Schema
1. In Supabase dashboard, go to **SQL Editor**
2. Copy and paste the contents of `lib/database/schema.sql`
3. Click **Run** to create all tables

### 6. Add Sample Data (Optional)
1. In SQL Editor, copy and paste the contents of `lib/database/seed.sql`
2. Click **Run** to insert sample data

### 7. Validate Setup
```bash
yarn validate-env
```

### 8. Start Development Server
```bash
yarn dev
```

## 🎯 Test the Application

### Test User Registration
1. Go to `http://localhost:3000`
2. Click "Get Started" to go through onboarding
3. Complete all steps with test data
4. You should be able to sign in immediately (no email verification needed)

### Test Login
1. Go to `http://localhost:3000/login`
2. Use the credentials you created during onboarding
3. You should be redirected to the dashboard

### Sample Test Data
Use this data for testing:

**Company Information:**
- Company Size: 1-10 Employees
- Transaction Volume: Less than 10,000 USD
- Role: Business Owner
- Primary Interest: Account management

**Account Details:**
- Email: <EMAIL>
- Name: John Doe
- Password: password123
- Business: Your Test Company
- Location: United States

## 🔧 Troubleshooting

### "Email not confirmed" Error
- Make sure you disabled email confirmation in Supabase settings
- Check Authentication → Settings → User Signups → Uncheck "Enable email confirmations"

### Environment Variables Not Found
- Run `yarn validate-env` to check your configuration
- Make sure `.env.local` is in the root directory
- Verify your Supabase URL and keys are correct

### Database Errors
- Make sure you ran the schema.sql file in Supabase SQL Editor
- Check that all tables were created successfully
- Verify RLS policies are enabled

### Connection Issues
- Check your Supabase project is active and running
- Verify your API keys are correct
- Make sure your project URL is correct

## 📚 Next Steps

Once you have the basic setup working:

1. **Read the full setup guide**: [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)
2. **Explore the codebase**: Check out the project structure in [README.md](./README.md)
3. **Customize for production**: Enable email confirmation and add proper security
4. **Add your branding**: Update colors, logos, and content

## 🔒 Production Considerations

**Before deploying to production:**

1. **Enable email confirmation** in Supabase settings
2. **Set up proper environment variables** for production
3. **Configure custom domain** and SSL
4. **Review and update RLS policies** for your security requirements
5. **Set up monitoring and backups**

## 🆘 Need Help?

- Check the detailed setup guide: [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)
- Review the project documentation: [README.md](./README.md)
- Run environment validation: `yarn validate-env`

Happy coding! 🚀
