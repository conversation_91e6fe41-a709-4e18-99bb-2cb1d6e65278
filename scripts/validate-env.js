#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates that all required environment variables are set
 */

const fs = require('fs');
const path = require('path');

const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
];

const optionalVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'DATABASE_URL',
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_APP_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'COLUMN_API_KEY',
    'COLUMN_BASE_URL',
    'COLUMN_ENVIRONMENT',
    'COLUMN_WEBHOOK_SECRET',
    'COLUMN_WEBHOOK_URL'
];

function loadEnvFile() {
    const envPath = path.join(process.cwd(), '.env.local');
    
    if (!fs.existsSync(envPath)) {
        console.error('❌ .env.local file not found!');
        console.log('📝 Please create a .env.local file in the root directory.');
        console.log('📖 See SUPABASE_SETUP.md for instructions.');
        process.exit(1);
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
            const [key, ...valueParts] = trimmed.split('=');
            if (key && valueParts.length > 0) {
                envVars[key.trim()] = valueParts.join('=').trim();
            }
        }
    });

    return envVars;
}

function validateUrl(url, name) {
    try {
        new URL(url);
        return true;
    } catch (error) {
        console.error(`❌ Invalid URL format for ${name}: ${url}`);
        return false;
    }
}

function validateEnvironment() {
    console.log('🔍 Validating environment configuration...\n');

    const envVars = loadEnvFile();
    let isValid = true;

    // Check required variables
    console.log('📋 Required Variables:');
    requiredVars.forEach(varName => {
        if (envVars[varName]) {
            console.log(`✅ ${varName}: Set`);
            
            // Validate URLs
            if (varName.includes('URL')) {
                if (!validateUrl(envVars[varName], varName)) {
                    isValid = false;
                }
            }
        } else {
            console.log(`❌ ${varName}: Missing`);
            isValid = false;
        }
    });

    console.log('\n📋 Optional Variables:');
    optionalVars.forEach(varName => {
        if (envVars[varName]) {
            console.log(`✅ ${varName}: Set`);
            
            // Validate URLs
            if (varName.includes('URL')) {
                validateUrl(envVars[varName], varName);
            }
        } else {
            console.log(`⚠️  ${varName}: Not set (optional)`);
        }
    });

    console.log('\n' + '='.repeat(50));

    if (isValid) {
        console.log('✅ Environment configuration is valid!');
        console.log('🚀 You can now start the development server with: yarn dev');
        console.log('');
        console.log('📧 IMPORTANT: For development, disable email confirmation in Supabase:');
        console.log('   1. Go to Authentication → Settings in your Supabase dashboard');
        console.log('   2. Uncheck "Enable email confirmations" under User Signups');
        console.log('   3. Click Save');
        console.log('   This allows immediate login without email verification.');

        // Check for Column configuration
        if (!envVars['COLUMN_API_KEY']) {
            console.log('');
            console.log('💡 Column Banking Features (Optional):');
            console.log('   To enable real banking features (accounts, transfers, etc.):');
            console.log('   1. Sign up at https://dashboard.column.com/register');
            console.log('   2. Get your sandbox API key');
            console.log('   3. Add COLUMN_API_KEY=test_xxxxxxxxxxxxxxxx to .env.local');
            console.log('   4. See COLUMN_SETUP.md for detailed instructions');
            console.log('   5. Visit /column-test to test the integration');
        }
    } else {
        console.log('❌ Environment configuration has issues!');
        console.log('📖 Please check SUPABASE_SETUP.md for setup instructions.');
        console.log('🔧 Fix the missing/invalid variables and run this script again.');
        process.exit(1);
    }
}

function showHelp() {
    console.log(`
🏦 equate - Environment Validator

Usage: node scripts/validate-env.js [options]

Options:
  --help, -h    Show this help message
  --example     Show example .env.local file

This script validates that all required environment variables are properly set.
`);
}

function showExample() {
    console.log(`
📝 Example .env.local file:

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Column Banking API Configuration
COLUMN_API_KEY=test_xxxxxxxxxxxxxxxx
COLUMN_BASE_URL=https://api.column.com
COLUMN_ENVIRONMENT=sandbox
COLUMN_WEBHOOK_SECRET=your-webhook-secret-here
COLUMN_WEBHOOK_URL=https://yourdomain.com/api/webhooks/column

# Database Configuration
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres

# App Configuration
NEXT_PUBLIC_APP_NAME="equate"
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Security
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

📖 For detailed setup instructions, see SUPABASE_SETUP.md
`);
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
}

if (args.includes('--example')) {
    showExample();
    process.exit(0);
}

// Run validation
validateEnvironment();
