import { NextRequest, NextResponse } from 'next/server';
import { columnWebhookService } from '@/lib/column/webhook-service';

export async function POST(request: NextRequest) {
    try {
        // Get the raw body as text for signature verification
        const body = await request.text();
        
        // Get the signature from headers
        const signature = request.headers.get('column-signature') || 
                         request.headers.get('x-column-signature') || '';
        
        if (!signature) {
            console.error('Missing webhook signature');
            return NextResponse.json(
                { error: 'Missing webhook signature' },
                { status: 400 }
            );
        }

        // Parse and verify the webhook payload
        const event = columnWebhookService.parseWebhookPayload(body, signature);
        
        if (!event) {
            console.error('Invalid webhook payload or signature');
            return NextResponse.json(
                { error: 'Invalid webhook payload or signature' },
                { status: 400 }
            );
        }

        console.log(`Received Column webhook: ${event.type} (${event.id})`);

        // Handle the event
        await columnWebhookService.handleEvent(event);

        // Return success response
        return NextResponse.json(
            { 
                received: true,
                event_id: event.id,
                event_type: event.type
            },
            { status: 200 }
        );

    } catch (error) {
        console.error('Error processing Column webhook:', error);
        
        // Return error response
        return NextResponse.json(
            { 
                error: 'Internal server error',
                message: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}

// Handle GET requests for webhook verification
export async function GET(request: NextRequest) {
    // Column may send GET requests to verify webhook endpoints
    const challenge = request.nextUrl.searchParams.get('challenge');
    
    if (challenge) {
        // Echo back the challenge for verification
        return NextResponse.json({ challenge });
    }
    
    return NextResponse.json(
        { 
            message: 'Column webhook endpoint is active',
            timestamp: new Date().toISOString()
        },
        { status: 200 }
    );
}

// Disable body parsing for raw signature verification
export const runtime = 'nodejs';
