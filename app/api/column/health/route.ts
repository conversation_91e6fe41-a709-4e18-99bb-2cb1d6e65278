import { NextRequest, NextResponse } from 'next/server';
import { columnService } from '@/lib/column/column-service';

export async function GET(request: NextRequest) {
    try {
        // Check if Column is configured
        const isConfigured = columnService.isConfigured();
        
        if (!isConfigured) {
            return NextResponse.json({
                configured: false,
                healthy: false,
                environment: 'unknown',
                message: 'Column API key not configured'
            });
        }

        // Check health
        const isHealthy = await columnService.healthCheck();
        const environment = columnService.getEnvironment();
        const isSandbox = columnService.isSandbox();

        return NextResponse.json({
            configured: true,
            healthy: isHealthy,
            environment,
            sandbox: isSandbox,
            message: isHealthy ? 'Column API is healthy' : 'Column API is not responding'
        });

    } catch (error) {
        console.error('Column health check error:', error);
        
        return NextResponse.json({
            configured: false,
            healthy: false,
            environment: 'unknown',
            error: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
}
