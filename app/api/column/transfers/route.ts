import { NextRequest, NextResponse } from 'next/server';
import { columnService } from '@/lib/column/column-service';

export async function GET(request: NextRequest) {
    try {
        // Check if Column is configured
        if (!columnService.isConfigured()) {
            return NextResponse.json({
                error: 'Column API not configured'
            }, { status: 400 });
        }

        // Get query parameters
        const { searchParams } = new URL(request.url);
        const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
        const cursor = searchParams.get('cursor') || undefined;
        const account_id = searchParams.get('account_id') || undefined;
        const type = searchParams.get('type') || undefined;
        const status = searchParams.get('status') || undefined;
        const start_date = searchParams.get('start_date') || undefined;
        const end_date = searchParams.get('end_date') || undefined;

        // List transfers
        const response = await columnService.listTransfers({
            limit,
            cursor,
            account_id,
            type,
            status,
            start_date,
            end_date
        });

        return NextResponse.json(response);

    } catch (error) {
        console.error('Column transfers list error:', error);
        
        return NextResponse.json({
            error: error instanceof Error ? error.message : 'Failed to list transfers'
        }, { status: 500 });
    }
}
