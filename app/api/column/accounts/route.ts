import { NextRequest, NextResponse } from 'next/server';
import { columnService } from '@/lib/column/column-service';

export async function GET(request: NextRequest) {
    try {
        // Check if Column is configured
        if (!columnService.isConfigured()) {
            return NextResponse.json({
                error: 'Column API not configured'
            }, { status: 400 });
        }

        // Get query parameters
        const { searchParams } = new URL(request.url);
        const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
        const cursor = searchParams.get('cursor') || undefined;
        const entity_id = searchParams.get('entity_id') || undefined;
        const status = searchParams.get('status') || undefined;

        // List bank accounts
        const response = await columnService.listBankAccounts({
            limit,
            cursor,
            entity_id,
            status
        });

        return NextResponse.json(response);

    } catch (error) {
        console.error('Column accounts list error:', error);
        
        return NextResponse.json({
            error: error instanceof Error ? error.message : 'Failed to list bank accounts'
        }, { status: 500 });
    }
}
