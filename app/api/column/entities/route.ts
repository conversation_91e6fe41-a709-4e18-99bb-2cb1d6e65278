import { NextRequest, NextResponse } from 'next/server';
import { columnService } from '@/lib/column/column-service';

export async function GET(request: NextRequest) {
    try {
        // Check if Column is configured
        if (!columnService.isConfigured()) {
            return NextResponse.json({
                error: 'Column API not configured'
            }, { status: 400 });
        }

        // Get query parameters
        const { searchParams } = new URL(request.url);
        const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
        const cursor = searchParams.get('cursor') || undefined;
        const status = searchParams.get('status') || undefined;
        const type = searchParams.get('type') as 'person' | 'business' | undefined;

        // List entities
        const response = await columnService.listEntities({
            limit,
            cursor,
            status,
            type
        });

        return NextResponse.json(response);

    } catch (error) {
        console.error('Column entities list error:', error);
        
        return NextResponse.json({
            error: error instanceof Error ? error.message : 'Failed to list entities'
        }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        // Check if Column is configured
        if (!columnService.isConfigured()) {
            return NextResponse.json({
                error: 'Column API not configured'
            }, { status: 400 });
        }

        const body = await request.json();
        const entityType = body.type || 'person';

        if (entityType === 'business') {
            // Create test business entity
            const entity = await columnService.createBusinessEntity({
                legal_name: `Test Business ${Date.now()}`,
                dba_name: `Test Corp ${Date.now()}`,
                ein: '123456789',
                phone: '+1234567890',
                email: `business.${Date.now()}@example.com`,
                address: {
                    line1: '123 Business St',
                    city: 'San Francisco',
                    state: 'CA',
                    postal_code: '94105',
                    country: 'US'
                },
                business_type: 'llc',
                industry: 'Technology',
                formation_date: '2020-01-01',
                formation_state: 'CA'
            });
            return NextResponse.json(entity);
        } else {
            // Create test person entity
            const entity = await columnService.createPersonEntity({
                first_name: 'Test',
                last_name: 'User',
                date_of_birth: '1990-01-01',
                ssn: '123456789',
                phone: '+1234567890',
                email: `test.user.${Date.now()}@example.com`,
                address: {
                    line1: '123 Test St',
                    city: 'San Francisco',
                    state: 'CA',
                    postal_code: '94105',
                    country: 'US'
                }
            });
            return NextResponse.json(entity);
        }

    } catch (error) {
        console.error('Column entity creation error:', error);

        return NextResponse.json({
            error: error instanceof Error ? error.message : 'Failed to create entity'
        }, { status: 500 });
    }
}
