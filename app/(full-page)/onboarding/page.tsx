'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Button } from 'primereact/button';
import { RadioButton } from 'primereact/radiobutton';
import { Checkbox } from 'primereact/checkbox';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { Dropdown } from 'primereact/dropdown';
import { Message } from 'primereact/message';
import { useRouter } from 'next/navigation';
import { LayoutContext } from '@/layout/context/layoutcontext';
import { AuthService } from '@/lib/auth/auth-service';
import { Database } from '@/types/database.types';

type UserRole = Database['public']['Tables']['profiles']['Row']['role'];

interface OnboardingData {
    companySize: string;
    transactionVolume: string;
    primaryInterest: string;
    additionalInterests: string[];
    businessEmail: string;
    firstName: string;
    lastName: string;
    password: string;
    businessName: string;
    businessLocation: string;
    mobileNumber: string;
    role: UserRole;
    agreeToTerms: boolean;
}

const Onboarding = () => {
    const { layoutConfig } = useContext(LayoutContext);
    const router = useRouter();
    const authService = new AuthService();

    const applyScale = () => {
        document.documentElement.style.fontSize = layoutConfig.scale + 'px';
    };

    useEffect(() => {
        applyScale();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [layoutConfig.scale]);

    const [currentStep, setCurrentStep] = useState(1);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [data, setData] = useState<OnboardingData>({
        companySize: '',
        transactionVolume: '',
        primaryInterest: '',
        additionalInterests: [],
        businessEmail: '',
        firstName: '',
        lastName: '',
        password: '',
        businessName: '',
        businessLocation: '',
        mobileNumber: '',
        role: 'employee',
        agreeToTerms: false
    });

    const companySizes = [
        { label: '1-10 Employees', value: '1-10' },
        { label: '11-50 Employees', value: '11-50' },
        { label: '51-200 Employees', value: '51-200' },
        { label: '201-500 Employees', value: '201-500' },
        { label: '500+ Employees', value: '500+' }
    ];

    const transactionVolumes = [
        { label: 'Less than 10,000 USD', value: 'less-10k' },
        { label: '10,000 to 50,000 USD', value: '10k-50k' },
        { label: '50,000 to 200,000 USD', value: '50k-200k' },
        { label: '200,000 to 1,000,000 USD', value: '200k-1m' },
        { label: 'Over 1,000,000 USD', value: 'over-1m' }
    ];

    const interests = [
        {
            id: 'account-management',
            label: 'Manage business accounts and track balances',
            icon: 'pi pi-wallet'
        },
        {
            id: 'money-transfers',
            label: 'Send and receive money transfers domestically and internationally',
            icon: 'pi pi-refresh'
        },
        {
            id: 'transaction-tracking',
            label: 'Track and categorize business transactions',
            icon: 'pi pi-chart-line'
        },
        {
            id: 'expense-management',
            label: 'Manage business expenses and reimbursements',
            icon: 'pi pi-file'
        },
        {
            id: 'financial-reporting',
            label: 'Generate financial reports and statements',
            icon: 'pi pi-chart-bar'
        },
        {
            id: 'api-integration',
            label: 'Integrate banking services with existing systems',
            icon: 'pi pi-code'
        }
    ];

    const roles = [
        { label: 'Business Owner', value: 'owner' },
        { label: 'Administrator', value: 'admin' },
        { label: 'Finance Manager', value: 'finance_manager' },
        { label: 'Finance Admin', value: 'finance_admin' },
        { label: 'Bookkeeper', value: 'bookkeeper' },
        { label: 'Employee', value: 'employee' },
        { label: 'Developer', value: 'developer' }
    ];

    const businessLocations = [
        { label: 'Select', value: '' },
        { label: 'United States', value: 'US' },
        { label: 'United Kingdom', value: 'UK' },
        { label: 'Australia', value: 'AU' },
        { label: 'Canada', value: 'CA' },
        { label: 'Singapore', value: 'SG' },
        { label: 'Hong Kong', value: 'HK' },
        { label: 'Other', value: 'OTHER' }
    ];

    const handleNext = () => {
        if (currentStep < 8) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handleBack = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleCreateAccount = async () => {
        setLoading(true);
        setError('');

        try {
            // Create account with Supabase
            const result = await authService.signUp(data.businessEmail, data.password, {
                full_name: `${data.firstName} ${data.lastName}`,
                role: data.role,
                employment_entity: data.businessName
            });

            // Check if user needs email confirmation
            if (result.user && !result.user.email_confirmed_at) {
                // User created but needs email confirmation
                setError('Account created! Please check your email and click the confirmation link, then return to sign in.');
                setTimeout(() => {
                    router.push('/login');
                }, 5000);
            } else {
                // User is confirmed, move to success step
                setCurrentStep(7);
            }
        } catch (err: any) {
            setError(err.message || 'Failed to create account');
        } finally {
            setLoading(false);
        }
    };

    const handleCompanySizeChange = (value: string) => {
        setData({ ...data, companySize: value });
    };

    const handleTransactionVolumeChange = (value: string) => {
        setData({ ...data, transactionVolume: value });
    };

    const handlePrimaryInterestChange = (value: string) => {
        setData({ ...data, primaryInterest: value });
    };



    const renderHeader = () => (
        <div className="flex align-items-center justify-content-between mb-6">
            <div className="flex align-items-center">
                <span className="text-blue-500 font-bold text-xl">equate</span>
            </div>
            {currentStep > 1 && <Button label="Back" icon="pi pi-arrow-left" className="p-button-text" onClick={handleBack} />}
        </div>
    );

    const renderStep1 = () => (
        <div className="text-center">
            <h2 className="text-xl font-bold mb-6">How big is your company?</h2>
            <div className="flex flex-column gap-3 max-w-md mx-auto">
                {companySizes.map((size) => (
                    <div
                        key={size.value}
                        className={`p-3 border-1 border-round cursor-pointer transition-colors ${data.companySize === size.value ? 'border-primary bg-primary-50' : 'border-300 hover:border-400'}`}
                        onClick={() => handleCompanySizeChange(size.value)}
                    >
                        <div className="flex align-items-center">
                            <RadioButton value={size.value} checked={data.companySize === size.value} onChange={(e) => handleCompanySizeChange(e.value)} className="mr-3" />
                            <span className="font-medium">{size.label}</span>
                        </div>
                    </div>
                ))}
            </div>
            <Button label="Continue" className="mt-6 px-6 py-3" onClick={handleNext} disabled={!data.companySize} />
        </div>
    );

    const renderStep2 = () => (
        <div className="text-center">
            <h2 className="text-xl font-bold mb-6">How much transaction volume do you expect to receive and send through equate every month?</h2>
            <div className="flex flex-column gap-3 max-w-md mx-auto">
                {transactionVolumes.map((volume) => (
                    <div
                        key={volume.value}
                        className={`p-3 border-1 border-round cursor-pointer transition-colors ${data.transactionVolume === volume.value ? 'border-primary bg-primary-50' : 'border-300 hover:border-400'}`}
                        onClick={() => handleTransactionVolumeChange(volume.value)}
                    >
                        <div className="flex align-items-center">
                            <RadioButton value={volume.value} checked={data.transactionVolume === volume.value} onChange={(e) => handleTransactionVolumeChange(e.value)} className="mr-3" />
                            <span className="font-medium">{volume.label}</span>
                        </div>
                    </div>
                ))}
            </div>
            <Button label="Continue" className="mt-6 px-6 py-3" onClick={handleNext} disabled={!data.transactionVolume} />
        </div>
    );

    const renderStep3 = () => (
        <div className="text-center">
            <h2 className="text-xl font-bold mb-2">What is your role in the organization?</h2>
            <p className="text-500 mb-6">This will help us customize your experience.</p>
            <div className="flex flex-column gap-3 max-w-md mx-auto mb-6">
                {roles.map((role) => (
                    <div
                        key={role.value}
                        className={`p-3 border-1 border-round cursor-pointer transition-colors ${data.role === role.value ? 'border-primary bg-primary-50' : 'border-300 hover:border-400'}`}
                        onClick={() => setData({ ...data, role: role.value as UserRole })}
                    >
                        <div className="flex align-items-center">
                            <RadioButton value={role.value} checked={data.role === role.value} onChange={(e) => setData({ ...data, role: e.value as UserRole })} className="mr-3" />
                            <span className="font-medium">{role.label}</span>
                        </div>
                    </div>
                ))}
            </div>
            <Button label="Continue" className="mt-6 px-6 py-3" onClick={handleNext} disabled={!data.role} />
        </div>
    );

    const renderStep4 = () => (
        <div className="text-center">
            <h2 className="text-xl font-bold mb-2">What banking services are you most interested in?</h2>
            <p className="text-500 mb-6">Select your primary area of interest.</p>
            <div className="flex flex-column gap-3 max-w-2xl mx-auto">
                {interests.map((interest) => (
                    <div
                        key={interest.id}
                        className={`p-4 border-1 border-round cursor-pointer transition-colors ${data.primaryInterest === interest.id ? 'border-primary bg-primary-50' : 'border-300 hover:border-400'}`}
                        onClick={() => handlePrimaryInterestChange(interest.id)}
                    >
                        <div className="flex align-items-center">
                            <i className={`${interest.icon} text-xl mr-3`}></i>
                            <span className="font-medium text-left">{interest.label}</span>
                        </div>
                    </div>
                ))}
            </div>
            <Button label="Continue" className="mt-6 px-6 py-3" onClick={handleNext} disabled={!data.primaryInterest} />
        </div>
    );

    const renderStep5 = () => (
        <div className="text-center">
            <h2 className="text-xl font-bold mb-6">Create your account</h2>

            {error && (
                <Message
                    severity="error"
                    text={error}
                    className="w-full mb-4 max-w-md mx-auto"
                />
            )}

            <div className="flex flex-column max-w-md mx-auto">
                <div className="field">
                    <label htmlFor="businessEmail" className="block text-left font-medium mb-2">
                        Business email *
                    </label>
                    <InputText
                        id="businessEmail"
                        value={data.businessEmail}
                        onChange={(e) => setData({ ...data, businessEmail: e.target.value })}
                        placeholder="<EMAIL>"
                        className="w-full"
                        type="email"
                        disabled={loading}
                    />
                </div>

                <div className="grid">
                    <div className="col-6 pb-0">
                        <div className="field">
                            <label htmlFor="firstName" className="block text-left font-medium mb-2">
                                First name *
                            </label>
                            <InputText
                                id="firstName"
                                value={data.firstName}
                                onChange={(e) => setData({ ...data, firstName: e.target.value })}
                                className="w-full"
                                disabled={loading}
                            />
                        </div>
                    </div>
                    <div className="col-6 pb-0">
                        <div className="field">
                            <label htmlFor="lastName" className="block text-left font-medium mb-2">
                                Last name *
                            </label>
                            <InputText
                                id="lastName"
                                value={data.lastName}
                                onChange={(e) => setData({ ...data, lastName: e.target.value })}
                                className="w-full"
                                disabled={loading}
                            />
                        </div>
                    </div>
                </div>

                <div className="field">
                    <label htmlFor="password" className="block text-left font-medium mb-2">
                        Password *
                    </label>
                    <Password
                        id="password"
                        value={data.password}
                        onChange={(e) => setData({ ...data, password: e.target.value })}
                        className="w-full"
                        toggleMask
                        feedback={true}
                        inputClassName="w-full"
                        disabled={loading}
                    />
                    <small className="text-500 mt-1 block text-left">Password must be at least 6 characters long</small>
                </div>

                <Button
                    label="Next"
                    className="w-full py-3 mt-4"
                    onClick={handleNext}
                    disabled={!data.businessEmail || !data.firstName || !data.lastName || !data.password || data.password.length < 6 || loading}
                    loading={loading}
                />
            </div>
        </div>
    );

    const renderStep6 = () => (
        <div className="text-center">
            <h2 className="text-xl font-bold mb-6">Complete your business information</h2>

            {error && (
                <Message
                    severity="error"
                    text={error}
                    className="w-full mb-4 max-w-md mx-auto"
                />
            )}

            <div className="flex flex-column max-w-md mx-auto">
                <div className="field">
                    <label htmlFor="businessName" className="block text-left font-medium mb-2">
                        Business name *
                    </label>
                    <InputText
                        id="businessName"
                        value={data.businessName}
                        onChange={(e) => setData({ ...data, businessName: e.target.value })}
                        className="w-full"
                        placeholder="Your Company Name"
                        disabled={loading}
                    />
                </div>

                <div className="field">
                    <label htmlFor="businessLocation" className="block text-left font-medium mb-2">
                        Business location *
                    </label>
                    <Dropdown
                        id="businessLocation"
                        value={data.businessLocation}
                        options={businessLocations}
                        onChange={(e) => setData({ ...data, businessLocation: e.value })}
                        className="w-full text-left"
                        placeholder="Select location"
                        disabled={loading}
                    />
                </div>

                <div className="field">
                    <label htmlFor="mobileNumber" className="block text-left font-medium mb-2">
                        Mobile number *
                    </label>
                    <div className="p-inputgroup">
                        <span className="p-inputgroup-addon">
                            <Dropdown value="+1" options={[{ label: '+1', value: '+1' }]} className="w-auto border-none" disabled={loading} />
                        </span>
                        <InputText
                            id="mobileNumber"
                            value={data.mobileNumber}
                            onChange={(e) => setData({ ...data, mobileNumber: e.target.value })}
                            className="flex-1"
                            placeholder="(*************"
                            disabled={loading}
                        />
                    </div>
                    <small className="text-500 mt-2 block text-left">We'll use this for account verification and security.</small>
                </div>

                <div className="field-checkbox mt-4">
                    <Checkbox
                        id="agreeToTerms"
                        checked={data.agreeToTerms}
                        onChange={(e) => setData({ ...data, agreeToTerms: e.checked || false })}
                        disabled={loading}
                    />
                    <label htmlFor="agreeToTerms" className="ml-2">
                        I agree to the{' '}
                        <a href="#" className="text-primary">
                            Terms & Conditions
                        </a>{' '}
                        and{' '}
                        <a href="#" className="text-primary">
                            Privacy Policy
                        </a>
                    </label>
                </div>

                <Button
                    label={loading ? "Creating account..." : "Create account"}
                    className="w-full py-3 mt-4"
                    onClick={handleCreateAccount}
                    disabled={!data.businessName || !data.businessLocation || !data.mobileNumber || !data.agreeToTerms || loading}
                    loading={loading}
                />
            </div>
        </div>
    );

    const renderStep7 = () => (
        <div className="text-center">
            <div className="mb-6">
                <i className="pi pi-check-circle text-green-500 text-6xl mb-4"></i>
                <h2 className="text-xl font-bold mb-4">Welcome to equate!</h2>
                <p className="text-md text-500 mb-6">Your account has been created successfully.</p>

                <div className="max-w-md mx-auto mb-6">
                    <div className="p-4 bg-green-50 border-1 border-green-200 border-round">
                        <h3 className="text-lg font-bold mb-2">Next Steps:</h3>
                        <ul className="text-left list-none p-0 m-0">
                            <li className="flex align-items-center mb-2">
                                <i className="pi pi-check text-green-500 mr-2"></i>
                                <span>Account created successfully</span>
                            </li>
                            <li className="flex align-items-center mb-2">
                                <i className="pi pi-building text-orange-500 mr-2"></i>
                                <span>Complete business verification</span>
                            </li>
                            <li className="flex align-items-center mb-2">
                                <i className="pi pi-wallet text-orange-500 mr-2"></i>
                                <span>Set up your business accounts</span>
                            </li>
                            <li className="flex align-items-center">
                                <i className="pi pi-users text-orange-500 mr-2"></i>
                                <span>Invite team members</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className="flex flex-column gap-3 max-w-md mx-auto">
                    <Button
                        label="Go to Login"
                        className="px-6 py-3"
                        onClick={() => router.push('/login')}
                    />
                    <Button
                        label="Back to Home"
                        className="px-6 py-3 p-button-outlined"
                        onClick={() => router.push('/')}
                    />
                </div>
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50 flex align-items-center justify-content-center p-4">
            <div className="lg:w-5 w-full">
                {renderHeader()}

                {currentStep === 1 && renderStep1()}
                {currentStep === 2 && renderStep2()}
                {currentStep === 3 && renderStep3()}
                {currentStep === 4 && renderStep4()}
                {currentStep === 5 && renderStep5()}
                {currentStep === 6 && renderStep6()}
                {currentStep === 7 && renderStep7()}
            </div>
        </div>
    );
};

export default Onboarding;
