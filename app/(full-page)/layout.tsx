import React from 'react';
import { Metadata } from 'next';
import AppConfig from '../../layout/AppConfig';
import { LayoutContext } from '@/layout/context/layoutcontext';

interface SimpleLayoutProps {
    children: React.ReactNode;
}

export const metadata: Metadata = {
    title: 'equate',
    description: 'Faster, Smarter & Borderless'
};

export default function SimpleLayout({ children }: SimpleLayoutProps) {

    return (
        <React.Fragment>
            {children}
            {/* <AppConfig simple /> */}
        </React.Fragment>
    );
}
