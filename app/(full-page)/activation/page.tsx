'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { FileUpload } from 'primereact/fileupload';
import { InputTextarea } from 'primereact/inputtextarea';
import { RadioButton } from 'primereact/radiobutton';
import { Checkbox } from 'primereact/checkbox';
import { InputNumber } from 'primereact/inputnumber';
import { Divider } from 'primereact/divider';
import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';
import { LayoutContext } from '@/layout/context/layoutcontext';
import { useRouter } from 'next/navigation';

interface ActivationData {
    // Business Details
    entityType: string;
    isRegistered: boolean;
    legalFirstName: string;
    legalMiddleName: string;
    legalLastName: string;
    countryRegion: string;
    businessAddress: string;
    addressLine2: string;
    city: string;
    state: string;
    zipCode: string;
    businessCommencementDate: Date | null;

    // Business Profile
    industry: string;
    productsServices: string;
    website: string;
    monthlyTurnover: string;
    businessLocation: string;

    // Business Owner
    ownerFirstName: string;
    ownerMiddleName: string;
    ownerLastName: string;
    ownerDateOfBirth: Date | null;
    ownerNationality: string;
    ownerAddress: string;
    ownerCity: string;
    ownerState: string;
    ownerZipCode: string;
    ownerPhoneNumber: string;
    ownerEmail: string;
    ownershipPercentage: number;

    // Documents
    businessDocuments: File[];
    identityDocuments: File[];
}

const AccountActivation = () => {
    const router = useRouter();

    const { layoutConfig } = useContext(LayoutContext);

    const applyScale = () => {
        document.documentElement.style.fontSize = layoutConfig.scale + 'px';
    };

    useEffect(() => {
        applyScale();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [layoutConfig.scale]);

    const [currentSection, setCurrentSection] = useState('get-started');
    const [currentSubSection, setCurrentSubSection] = useState('welcome');
    const [data, setData] = useState<ActivationData>({
        entityType: '',
        isRegistered: false,
        legalFirstName: '',
        legalMiddleName: '',
        legalLastName: '',
        countryRegion: 'United States of America',
        businessAddress: '',
        addressLine2: '',
        city: '',
        state: '',
        zipCode: '',
        businessCommencementDate: null,
        industry: '',
        productsServices: '',
        website: '',
        monthlyTurnover: '',
        businessLocation: '',
        ownerFirstName: '',
        ownerMiddleName: '',
        ownerLastName: '',
        ownerDateOfBirth: null,
        ownerNationality: '',
        ownerAddress: '',
        ownerCity: '',
        ownerState: '',
        ownerZipCode: '',
        ownerPhoneNumber: '',
        ownerEmail: '',
        ownershipPercentage: 100,
        businessDocuments: [],
        identityDocuments: []
    });

    const entityTypes = [
        { label: 'Sole proprietorship', value: 'sole-proprietorship' },
        { label: 'Company', value: 'company' },
        { label: 'Partnership', value: 'partnership' },
        { label: 'LLC', value: 'llc' },
        { label: 'Corporation', value: 'corporation' }
    ];

    const countries = [
        { label: 'United States of America', value: 'United States of America' },
        { label: 'Canada', value: 'Canada' },
        { label: 'United Kingdom', value: 'United Kingdom' },
        { label: 'Australia', value: 'Australia' }
    ];

    const states = [
        { label: 'Select', value: '' },
        { label: 'California', value: 'CA' },
        { label: 'New York', value: 'NY' },
        { label: 'Texas', value: 'TX' },
        { label: 'Florida', value: 'FL' }
    ];

    const industries = [
        { label: 'Technology', value: 'technology' },
        { label: 'E-commerce', value: 'ecommerce' },
        { label: 'Healthcare', value: 'healthcare' },
        { label: 'Finance', value: 'finance' },
        { label: 'Education', value: 'education' },
        { label: 'Manufacturing', value: 'manufacturing' },
        { label: 'Retail', value: 'retail' },
        { label: 'Other', value: 'other' }
    ];

    const monthlyTurnovers = [
        { label: 'Less than $10,000', value: 'less-10k' },
        { label: '$10,000 - $50,000', value: '10k-50k' },
        { label: '$50,000 - $100,000', value: '50k-100k' },
        { label: '$100,000 - $500,000', value: '100k-500k' },
        { label: 'More than $500,000', value: 'more-500k' }
    ];

    const menuItems = [
        {
            id: 'get-started',
            label: 'Get Started',
            icon: 'pi pi-play',
            completed: false,
            subItems: [
                { id: 'business-details', label: 'Business details', completed: false },
                { id: 'business-document', label: 'Business document', completed: false }
            ]
        },
        {
            id: 'business-profile',
            label: 'Business profile',
            icon: 'pi pi-building',
            completed: false,
            subItems: [
                { id: 'industry', label: 'Industry', completed: false },
                { id: 'products-services', label: 'Products or services', completed: false },
                { id: 'website', label: 'Website', completed: false },
                { id: 'monthly-turnover', label: 'Monthly turnover', completed: false },
                { id: 'business-location', label: 'Business location', completed: false }
            ]
        },
        {
            id: 'business-owner',
            label: 'Business owner',
            icon: 'pi pi-user',
            completed: false,
            subItems: [
                { id: 'list-owners', label: 'List of business owners', completed: false },
                { id: 'applicant-identity', label: "Applicant's identity", completed: false },
                { id: 'id-verification', label: 'ID verification', completed: false }
            ]
        }
    ];

    const renderSidebar = () => (
        <div className="w-full md:w-20rem bg-white border-right-1 surface-border p-4">
            <div className="mb-4">
                <div className="flex align-items-center gap-2 mb-4">
                    <span className="text-primary font-bold text-xl">equate</span>
                    <Button icon="pi pi-times" className="p-button-text p-button-plain ml-auto" onClick={() => router.push('/')} />
                </div>
            </div>

            {/* <div className="mb-6">
                <div className="flex align-items-center justify-content-center mb-4">
                    <div className="w-6rem h-6rem border-circle bg-orange-100 flex align-items-center justify-content-center">
                        <i className="pi pi-lock text-orange-500 text-xl"></i>
                    </div>
                </div>
                <h3 className="text-center text-xl font-bold mb-2">Unlock the full power of equate</h3>
                <p className="text-center text-500 text-sm mb-4">
                    We're excited to have you on board. Help us understand and verify your business in order to activate your account.
                </p>
            </div> */}

            <div className="mb-6">
                {/* <h4 className="font-bold mb-3">Upcoming steps</h4> */}
                <div className="flex flex-column gap-2">
                    {menuItems.map((item) => (
                        <div key={item.id}>
                            <div
                                className={`flex align-items-center gap-2 cursor-pointer p-2 border-round ${currentSection === item.id ? 'text-primary font-bold' : 'text-700 hover:bg-gray-50'}`}
                                onClick={() => {
                                    setCurrentSection(item.id);
                                    setCurrentSubSection(item.subItems[0].id);
                                }}
                            >
                                <i className={`${item.icon} text-sm`}></i>
                                <span className="text-md flex-1">{item.label}</span>
                                {currentSection === item.id ? <i className="pi pi-chevron-up text-xs"></i> : <i className="pi pi-chevron-down text-xs"></i>}
                            </div>

                            {item.subItems && currentSection === item.id && (
                                <div className="ml-4 mt-1">
                                    {item.subItems.map((subItem) => (
                                        <div
                                            key={subItem.id}
                                            className={`py-2 cursor-pointer text-sm border-round px-2 ${currentSubSection === subItem.id ? 'text-primary font-bold bg-primary-50' : 'text-500 hover:bg-gray-50'}`}
                                            onClick={() => setCurrentSubSection(subItem.id)}
                                        >
                                            {subItem.label}
                                            {subItem.completed && <i className="pi pi-check text-green-500 ml-2"></i>}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* <Button
                label="Get started"
                className="w-full"
                onClick={() => {
                    setCurrentSection('get-started');
                    setCurrentSubSection('welcome');
                }}
            /> */}
        </div>
    );

    const renderWelcome = () => (
        <div className="text-center max-w-2xl mx-auto">
            <div className="mb-6">
                <div className="flex align-items-center justify-content-center mb-6">
                    <div className="w-6rem h-6rem border-circle bg-blue-100 flex align-items-center justify-content-center">
                        <i className="pi pi-lock text-blue-500 text-4xl"></i>
                    </div>
                </div>
                <h2 className="text-xl font-bold mb-2">Unlock the full power of equate</h2>
                <p className="text-500 text-md mb-6">We&apos;re excited to have you on board. Help us understand and verify your business in order to activate your account.</p>
            </div>

            <Card className="mb-6">
                <div className="p-4">
                    <h3 className="font-bold mb-3 text-left text-xl">Business registered in</h3>
                    <p className="text-500 text-md mb-4 text-left">Please make sure you have chosen the correct country or region where your business is registered. You will not be allowed to change it after you proceed.</p>
                    <div className="flex align-items-center gap-2">
                        {/* <img src="https://flagcdn.com/w20/us.png" alt="US Flag" className="w-1rem h-auto" /> */}
                        <Dropdown value="United States of America" options={[{ label: 'United States of America', value: 'United States of America' }]} className="w-full text-left" />
                    </div>
                </div>
            </Card>

            <Button label="Get Started" className="px-8 py-3" onClick={() => setCurrentSubSection('business-details')} />
        </div>
    );

    const renderBusinessDetails = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Business details</h2>
            <p className="text-500 mb-2">Before we get started, we need to confirm some of your business details. Depending on the entity type, you might also be required to provide relevant business documents.</p>
            <p className="text-500 mb-5">Please note that we do not support businesses with shares held in the form of bearer shares. By continuing, you are confirming that your business does not include such shares.</p>

            <div className="grid">
                <div className="col-12">
                    <div className="field mb-4">
                        <label htmlFor="entityType" className="block font-medium mb-2">
                            Entity type
                        </label>
                        <Dropdown id="entityType" value={data.entityType} options={entityTypes} onChange={(e) => setData({ ...data, entityType: e.value })} placeholder="Sole proprietorship" className="w-full" />
                        <a href="#" className="text-primary text-sm mt-2 inline-block">
                            Unsure about your entity type?
                        </a>

                        {data.entityType === 'llc' && (
                            <div className="mt-3 p-3 bg-blue-50 border-1 border-blue-200 border-round">
                                <div className="flex align-items-start gap-2">
                                    <i className="pi pi-info-circle text-blue-500 mt-1"></i>
                                    <span className="text-blue-700 text-sm">If your business is an LLC, please choose company instead</span>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="field mb-4">
                        <label className="block font-medium mb-3">Is your business registered?</label>
                        <p className="text-500 text-sm mb-3">Registered businesses receive greater transaction limits.</p>
                        <div className="flex gap-3">
                            <Button label="Yes" className={`flex-1 ${data.isRegistered ? 'p-button-outlined' : ''}`} onClick={() => setData({ ...data, isRegistered: true })} />
                            <Button label="No" className={`flex-1 ${!data.isRegistered ? 'p-button-outlined' : ''}`} onClick={() => setData({ ...data, isRegistered: false })} />
                        </div>
                    </div>

                    <Divider />

                    <h3 className="text-xl font-bold mb-4">Your business details</h3>
                    <p className="text-500 mb-4">Please provide the following details about your business and owner</p>

                    <div className="grid">
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor="legalFirstName" className="block font-medium mb-2">
                                    Legal first name
                                </label>
                                <InputText id="legalFirstName" value={data.legalFirstName} onChange={(e) => setData({ ...data, legalFirstName: e.target.value })} className="w-full" />
                            </div>
                        </div>
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor="legalMiddleName" className="block font-medium mb-2">
                                    Legal middle name <span className="text-500">Optional</span>
                                </label>
                                <InputText id="legalMiddleName" value={data.legalMiddleName} onChange={(e) => setData({ ...data, legalMiddleName: e.target.value })} className="w-full" />
                            </div>
                        </div>
                    </div>

                    <div className="field">
                        <label htmlFor="legalLastName" className="block font-medium mb-2">
                            Legal last name
                        </label>
                        <InputText id="legalLastName" value={data.legalLastName} onChange={(e) => setData({ ...data, legalLastName: e.target.value })} className="w-full" />
                    </div>

                    <div className="field">
                        <label htmlFor="countryRegion" className="block font-medium mb-2">
                            Country or region
                        </label>
                        <Dropdown id="countryRegion" value={data.countryRegion} options={countries} onChange={(e) => setData({ ...data, countryRegion: e.value })} className="w-full" />
                    </div>

                    <div className="field">
                        <label htmlFor="businessAddress" className="block font-medium mb-2">
                            Principal place of business address
                            <i className="pi pi-info-circle ml-2 text-500"></i>
                        </label>
                        <InputText id="businessAddress" value={data.businessAddress} onChange={(e) => setData({ ...data, businessAddress: e.target.value })} placeholder="Enter street address to search" className="w-full" />
                    </div>

                    <div className="field">
                        <label htmlFor="addressLine2" className="block font-medium mb-2">
                            Address line 2 <span className="text-500">Optional</span>
                        </label>
                        <InputText id="addressLine2" value={data.addressLine2} onChange={(e) => setData({ ...data, addressLine2: e.target.value })} placeholder="Apt., suite, building #, etc." className="w-full" />
                    </div>

                    <div className="field">
                        <label htmlFor="city" className="block font-medium mb-2">
                            City
                        </label>
                        <InputText id="city" value={data.city} onChange={(e) => setData({ ...data, city: e.target.value })} className="w-full" />
                    </div>

                    <div className="grid">
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor="state" className="block font-medium mb-2">
                                    State
                                </label>
                                <Dropdown id="state" value={data.state} options={states} onChange={(e) => setData({ ...data, state: e.value })} placeholder="Select" className="w-full" />
                            </div>
                        </div>
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor="zipCode" className="block font-medium mb-2">
                                    ZIP code
                                </label>
                                <InputText id="zipCode" value={data.zipCode} onChange={(e) => setData({ ...data, zipCode: e.target.value })} className="w-full" />
                            </div>
                        </div>
                    </div>

                    <div className="field">
                        <label htmlFor="businessCommencementDate" className="block font-medium mb-2">
                            Business commencement date
                            <i className="pi pi-info-circle ml-2 text-500"></i>
                        </label>
                        <Calendar id="businessCommencementDate" value={data.businessCommencementDate} onChange={(e) => setData({ ...data, businessCommencementDate: e.value || null })} placeholder="yyyy-mm-dd" dateFormat="yy-mm-dd" className="w-full" />
                    </div>

                    <div className="flex justify-content-end mt-6">
                        <Button label="Save & Next" onClick={() => setCurrentSubSection('business-document')} className="px-6" />
                    </div>
                </div>
            </div>
        </div>
    );

    const renderBusinessDocument = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Business document</h2>
            <p className="text-500 mb-6">Please upload your business registration documents to verify your business details.</p>

            <Card className="mb-4">
                <div className="text-center p-4">
                    <i className="pi pi-upload text-4xl text-500 mb-3"></i>
                    <h3 className="text-lg font-bold mb-2">Upload Business Documents</h3>
                    <p className="text-500 mb-4">Accepted formats: PDF, JPG, PNG (Max 10MB per file)</p>
                    <FileUpload mode="basic" name="businessDocs" accept=".pdf,.jpg,.jpeg,.png" maxFileSize={10000000} chooseLabel="Choose Files" className="p-button-outlined" />
                </div>
            </Card>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('business-details')} />
                <Button
                    label="Save & Next"
                    onClick={() => {
                        setCurrentSection('business-profile');
                        setCurrentSubSection('industry');
                    }}
                    className="px-6"
                />
            </div>
        </div>
    );

    const renderIndustry = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Industry</h2>
            <p className="text-500 mb-6">Please select the industry that best describes your business.</p>

            <div className="field">
                <label htmlFor="industry" className="block font-medium mb-2">
                    Business Industry
                </label>
                <Dropdown id="industry" value={data.industry} options={industries} onChange={(e) => setData({ ...data, industry: e.value })} placeholder="Select your industry" className="w-full" />
            </div>

            <div className="flex justify-content-between mt-6">
                <Button
                    label="Back"
                    icon="pi pi-arrow-left"
                    className="p-button-outlined"
                    onClick={() => {
                        setCurrentSection('get-started');
                        setCurrentSubSection('business-document');
                    }}
                />
                <Button label="Save & Next" onClick={() => setCurrentSubSection('products-services')} className="px-6" />
            </div>
        </div>
    );

    const renderProductsServices = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Products or services</h2>
            <p className="text-500 mb-6">Please describe the main products or services your business offers.</p>

            <div className="field">
                <label htmlFor="productsServices" className="block font-medium mb-2">
                    Describe your products or services
                </label>
                <InputTextarea
                    id="productsServices"
                    value={data.productsServices}
                    onChange={(e) => setData({ ...data, productsServices: e.target.value })}
                    rows={5}
                    placeholder="Describe what your business does, what products you sell, or what services you provide..."
                    className="w-full"
                />
            </div>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('industry')} />
                <Button label="Save & Next" onClick={() => setCurrentSubSection('website')} className="px-6" />
            </div>
        </div>
    );

    const renderWebsite = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Website</h2>
            <p className="text-500 mb-6">Please provide your business website URL if you have one.</p>

            <div className="field">
                <label htmlFor="website" className="block font-medium mb-2">
                    Business Website <span className="text-500">Optional</span>
                </label>
                <InputText id="website" value={data.website} onChange={(e) => setData({ ...data, website: e.target.value })} placeholder="https://www.yourwebsite.com" className="w-full" />
                <small className="text-500">If you don&apos;t have a website, you can skip this step.</small>
            </div>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('products-services')} />
                <Button label="Save & Next" onClick={() => setCurrentSubSection('monthly-turnover')} className="px-6" />
            </div>
        </div>
    );

    const renderMonthlyTurnover = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Monthly turnover</h2>
            <p className="text-500 mb-6">Please provide your expected monthly transaction volume.</p>

            <div className="field">
                <label htmlFor="monthlyTurnover" className="block font-medium mb-2">
                    Expected Monthly Turnover
                </label>
                <Dropdown id="monthlyTurnover" value={data.monthlyTurnover} options={monthlyTurnovers} onChange={(e) => setData({ ...data, monthlyTurnover: e.value })} placeholder="Select monthly turnover range" className="w-full" />
            </div>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('website')} />
                <Button label="Save & Next" onClick={() => setCurrentSubSection('business-location')} className="px-6" />
            </div>
        </div>
    );

    const renderBusinessLocation = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Business location</h2>
            <p className="text-500 mb-6">Please confirm your business location details.</p>

            <div className="field">
                <label htmlFor="businessLocation" className="block font-medium mb-2">
                    Primary Business Location
                </label>
                <Dropdown id="businessLocation" value={data.businessLocation} options={countries} onChange={(e) => setData({ ...data, businessLocation: e.value })} placeholder="Select business location" className="w-full" />
            </div>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('monthly-turnover')} />
                <Button
                    label="Save & Next"
                    onClick={() => {
                        setCurrentSection('business-owner');
                        setCurrentSubSection('list-owners');
                    }}
                    className="px-6"
                />
            </div>
        </div>
    );

    const renderListOwners = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">List of business owners</h2>
            <p className="text-500 mb-6">Please provide information about all business owner.</p>

            <Card className="mb-4">
                <h3 className="text-lg font-bold mb-4">Business Owner</h3>

                <div className="grid">
                    <div className="col-12 md:col-4">
                        <div className="field">
                            <label htmlFor="ownerFirstName" className="block font-medium mb-2">
                                First name
                            </label>
                            <InputText id="ownerFirstName" value={data.ownerFirstName} onChange={(e) => setData({ ...data, ownerFirstName: e.target.value })} className="w-full" />
                        </div>
                    </div>
                    <div className="col-12 md:col-4">
                        <div className="field">
                            <label htmlFor="ownerMiddleName" className="block font-medium mb-2">
                                Middle name <span className="text-500">Optional</span>
                            </label>
                            <InputText id="ownerMiddleName" value={data.ownerMiddleName} onChange={(e) => setData({ ...data, ownerMiddleName: e.target.value })} className="w-full" />
                        </div>
                    </div>
                    <div className="col-12 md:col-4">
                        <div className="field">
                            <label htmlFor="ownerLastName" className="block font-medium mb-2">
                                Last name
                            </label>
                            <InputText id="ownerLastName" value={data.ownerLastName} onChange={(e) => setData({ ...data, ownerLastName: e.target.value })} className="w-full" />
                        </div>
                    </div>
                </div>

                <div className="grid">
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor="ownerDateOfBirth" className="block font-medium mb-2">
                                Date of birth
                            </label>
                            <Calendar id="ownerDateOfBirth" value={data.ownerDateOfBirth} onChange={(e) => setData({ ...data, ownerDateOfBirth: e.value || null })} placeholder="yyyy-mm-dd" dateFormat="yy-mm-dd" className="w-full" />
                        </div>
                    </div>
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor="ownershipPercentage" className="block font-medium mb-2">
                                Ownership percentage
                            </label>
                            <InputNumber id="ownershipPercentage" value={data.ownershipPercentage} onValueChange={(e) => setData({ ...data, ownershipPercentage: e.value || 0 })} suffix="%" min={0} max={100} className="w-full" />
                        </div>
                    </div>
                </div>

                <div className="field">
                    <label htmlFor="ownerNationality" className="block font-medium mb-2">
                        Nationality
                    </label>
                    <Dropdown id="ownerNationality" value={data.ownerNationality} options={countries} onChange={(e) => setData({ ...data, ownerNationality: e.value })} placeholder="Select nationality" className="w-full" />
                </div>

                <div className="field">
                    <label htmlFor="ownerEmail" className="block font-medium mb-2">
                        Email address
                    </label>
                    <InputText id="ownerEmail" value={data.ownerEmail} onChange={(e) => setData({ ...data, ownerEmail: e.target.value })} className="w-full" />
                </div>

                <div className="field">
                    <label htmlFor="ownerPhoneNumber" className="block font-medium mb-2">
                        Phone number
                    </label>
                    <InputText id="ownerPhoneNumber" value={data.ownerPhoneNumber} onChange={(e) => setData({ ...data, ownerPhoneNumber: e.target.value })} className="w-full" />
                </div>
            </Card>

            <div className="flex justify-content-between mt-6">
                <Button
                    label="Back"
                    icon="pi pi-arrow-left"
                    className="p-button-outlined"
                    onClick={() => {
                        setCurrentSection('business-profile');
                        setCurrentSubSection('business-location');
                    }}
                />
                <Button label="Save & Next" onClick={() => setCurrentSubSection('applicant-identity')} className="px-6" />
            </div>
        </div>
    );

    const renderApplicantIdentity = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">Applicant&apos;s identity</h2>
            <p className="text-500 mb-6">Please provide your residential address and contact information.</p>

            <div className="field">
                <label htmlFor="ownerAddress" className="block font-medium mb-2">
                    Residential address
                </label>
                <InputText id="ownerAddress" value={data.ownerAddress} onChange={(e) => setData({ ...data, ownerAddress: e.target.value })} placeholder="Enter street address" className="w-full" />
            </div>

            <div className="field">
                <label htmlFor="ownerCity" className="block font-medium mb-2">
                    City
                </label>
                <InputText id="ownerCity" value={data.ownerCity} onChange={(e) => setData({ ...data, ownerCity: e.target.value })} className="w-full" />
            </div>

            <div className="grid">
                <div className="col-12 md:col-6">
                    <div className="field">
                        <label htmlFor="ownerState" className="block font-medium mb-2">
                            State
                        </label>
                        <Dropdown id="ownerState" value={data.ownerState} options={states} onChange={(e) => setData({ ...data, ownerState: e.value })} placeholder="Select" className="w-full" />
                    </div>
                </div>
                <div className="col-12 md:col-6">
                    <div className="field">
                        <label htmlFor="ownerZipCode" className="block font-medium mb-2">
                            ZIP code
                        </label>
                        <InputText id="ownerZipCode" value={data.ownerZipCode} onChange={(e) => setData({ ...data, ownerZipCode: e.target.value })} className="w-full" />
                    </div>
                </div>
            </div>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('list-owners')} />
                <Button label="Save & Next" onClick={() => setCurrentSubSection('id-verification')} className="px-6" />
            </div>
        </div>
    );

    const renderIdVerification = () => (
        <div>
            <h2 className="text-xl font-bold mb-4">ID verification</h2>
            <p className="text-500 mb-6">Please upload a government-issued photo ID for identity verification.</p>

            <Card className="mb-4">
                <div className="text-center p-4">
                    <i className="pi pi-id-card text-4xl text-500 mb-3"></i>
                    <h3 className="text-lg font-bold mb-2">Upload Identity Document</h3>
                    <p className="text-500 mb-4">
                        Accepted documents: Driver&apos;s License, Passport, National ID
                        <br />
                        Formats: PDF, JPG, PNG (Max 10MB per file)
                    </p>
                    <FileUpload mode="basic" name="identityDocs" accept=".pdf,.jpg,.jpeg,.png" maxFileSize={10000000} chooseLabel="Choose Files" className="p-button-outlined" />
                </div>
            </Card>

            <div className="p-4 bg-blue-50 border-1 border-blue-200 border-round mb-4">
                <h4 className="font-bold mb-2">Document Requirements:</h4>
                <ul className="list-none p-0 m-0">
                    <li className="flex align-items-center mb-2">
                        <i className="pi pi-check text-green-500 mr-2"></i>
                        <span>Document must be in color</span>
                    </li>
                    <li className="flex align-items-center mb-2">
                        <i className="pi pi-check text-green-500 mr-2"></i>
                        <span>All four corners must be visible</span>
                    </li>
                    <li className="flex align-items-center mb-2">
                        <i className="pi pi-check text-green-500 mr-2"></i>
                        <span>Text must be clearly readable</span>
                    </li>
                    <li className="flex align-items-center">
                        <i className="pi pi-check text-green-500 mr-2"></i>
                        <span>Document must not be expired</span>
                    </li>
                </ul>
            </div>

            <div className="flex justify-content-between mt-6">
                <Button label="Back" icon="pi pi-arrow-left" className="p-button-outlined" onClick={() => setCurrentSubSection('applicant-identity')} />
                <Button
                    label="Submit Application"
                    onClick={() => {
                        // Handle form submission
                        alert('Application submitted successfully!');
                    }}
                    className="px-6"
                />
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50 flex">
            {renderSidebar()}
            <div className="flex-1 p-6">
                <div className="max-w-4xl mx-auto">
                    {currentSection === 'get-started' && currentSubSection === 'welcome' && renderWelcome()}
                    {currentSection === 'get-started' && currentSubSection === 'business-details' && renderBusinessDetails()}
                    {currentSection === 'get-started' && currentSubSection === 'business-document' && renderBusinessDocument()}
                    {currentSection === 'business-profile' && currentSubSection === 'industry' && renderIndustry()}
                    {currentSection === 'business-profile' && currentSubSection === 'products-services' && renderProductsServices()}
                    {currentSection === 'business-profile' && currentSubSection === 'website' && renderWebsite()}
                    {currentSection === 'business-profile' && currentSubSection === 'monthly-turnover' && renderMonthlyTurnover()}
                    {currentSection === 'business-profile' && currentSubSection === 'business-location' && renderBusinessLocation()}
                    {currentSection === 'business-owner' && currentSubSection === 'list-owners' && renderListOwners()}
                    {currentSection === 'business-owner' && currentSubSection === 'applicant-identity' && renderApplicantIdentity()}
                    {currentSection === 'business-owner' && currentSubSection === 'id-verification' && renderIdVerification()}
                </div>
            </div>
        </div>
    );
};

export default AccountActivation;
