/* eslint-disable @next/next/no-img-element */
'use client';
import { useRouter } from 'next/navigation';
import React, { useContext, useEffect, useState } from 'react';
import { Button } from 'primereact/button';
import { Password } from 'primereact/password';
import { LayoutContext } from '../../../../layout/context/layoutcontext';
import { InputText } from 'primereact/inputtext';
import { classNames } from 'primereact/utils';
import { Message } from 'primereact/message';
import { Dropdown } from 'primereact/dropdown';
import { AuthService } from '@/lib/auth/auth-service';

const SignupPage = () => {
    const { layoutConfig } = useContext(LayoutContext);
    const applyScale = () => {
        document.documentElement.style.fontSize = layoutConfig.scale + 'px';
    };

    useEffect(() => {
        applyScale();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [layoutConfig.scale]);

    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [fullName, setFullName] = useState('');
    const [role, setRole] = useState('employee');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const router = useRouter();
    const authService = new AuthService();

    const roleOptions = [
        { label: 'Employee', value: 'employee' },
        { label: 'Admin', value: 'admin' },
        { label: 'Finance Admin', value: 'finance_admin' },
        { label: 'Finance Manager', value: 'finance_manager' },
        { label: 'Bookkeeper', value: 'bookkeeper' },
        { label: 'Developer', value: 'developer' }
    ];

    const handleSignUp = async () => {
        if (!email || !password || !fullName) {
            setError('Please fill in all required fields');
            return;
        }

        if (password !== confirmPassword) {
            setError('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            setError('Password must be at least 6 characters long');
            return;
        }

        setLoading(true);
        setError('');

        try {
            await authService.signUp(email, password, {
                full_name: fullName,
                role: role as any,
                employment_entity: 'CONNECTING MATRIX LLC'
            });
            
            setError('Account created successfully! Please check your email to verify your account.');
            setTimeout(() => {
                router.push('/login');
            }, 3000);
        } catch (err: any) {
            setError(err.message || 'Failed to create account');
        } finally {
            setLoading(false);
        }
    };

    const containerClassName = classNames('surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden', { 'p-input-filled': layoutConfig.inputStyle === 'filled' });

    return (
        <div className={containerClassName}>
            <div className="flex flex-column align-items-center justify-content-center">
                <div
                    style={{
                        borderRadius: '56px',
                        padding: '0.3rem',
                        background: 'linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)'
                    }}
                >
                    <div className="w-full surface-card py-8 px-5 sm:px-8" style={{ borderRadius: '53px' }}>
                        <div className="text-center mb-6">
                            <div className="text-primary text-4xl font-bold mb-3">equate</div>
                            <p className="text-600 text-lg">Create your account</p>
                        </div>

                        <div>
                            {error && (
                                <Message 
                                    severity={error.includes('successfully') ? 'success' : 'error'} 
                                    text={error} 
                                    className="w-full mb-4"
                                />
                            )}

                            <label htmlFor="fullName" className="block text-900 text-xl font-medium mb-2">
                                Full Name *
                            </label>
                            <InputText 
                                id="fullName" 
                                type="text" 
                                placeholder="Enter your full name" 
                                className="w-full md:w-30rem mb-4" 
                                style={{ padding: '1rem' }}
                                value={fullName}
                                onChange={(e) => setFullName(e.target.value)}
                                disabled={loading}
                            />

                            <label htmlFor="email" className="block text-900 text-xl font-medium mb-2">
                                Email *
                            </label>
                            <InputText 
                                id="email" 
                                type="email" 
                                placeholder="Enter your email address" 
                                className="w-full md:w-30rem mb-4" 
                                style={{ padding: '1rem' }}
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                disabled={loading}
                            />

                            <label htmlFor="role" className="block text-900 text-xl font-medium mb-2">
                                Role
                            </label>
                            <Dropdown 
                                id="role"
                                value={role} 
                                options={roleOptions} 
                                onChange={(e) => setRole(e.value)} 
                                placeholder="Select your role"
                                className="w-full md:w-30rem mb-4"
                                disabled={loading}
                            />

                            <label htmlFor="password" className="block text-900 font-medium text-xl mb-2">
                                Password *
                            </label>
                            <Password 
                                inputId="password" 
                                value={password} 
                                onChange={(e) => setPassword(e.target.value)} 
                                placeholder="Enter your password" 
                                toggleMask 
                                className="w-full mb-4" 
                                inputClassName="w-full p-3 md:w-30rem"
                                disabled={loading}
                            />

                            <label htmlFor="confirmPassword" className="block text-900 font-medium text-xl mb-2">
                                Confirm Password *
                            </label>
                            <Password 
                                inputId="confirmPassword" 
                                value={confirmPassword} 
                                onChange={(e) => setConfirmPassword(e.target.value)} 
                                placeholder="Confirm your password" 
                                toggleMask 
                                className="w-full mb-5" 
                                inputClassName="w-full p-3 md:w-30rem"
                                disabled={loading}
                                feedback={false}
                            />
                            
                            <Button 
                                label={loading ? 'Creating Account...' : 'Create Account'} 
                                className="w-full p-3 text-xl mb-4" 
                                onClick={handleSignUp}
                                loading={loading}
                                disabled={loading}
                            />
                            
                            <div className='text-center'>
                                <a 
                                    className="font-medium no-underline cursor-pointer" 
                                    style={{ color: 'var(--primary-color)' }}
                                    onClick={() => router.push('/login')}
                                >
                                    Already have an account? Sign in
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SignupPage;
