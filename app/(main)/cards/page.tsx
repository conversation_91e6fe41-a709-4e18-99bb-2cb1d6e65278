'use client';
import React, { useState } from 'react';
import { Tab<PERSON>iew, TabPanel } from 'primereact/tabview';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Card } from 'primereact/card';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';
import { Checkbox } from 'primereact/checkbox';
import { Calendar } from 'primereact/calendar';
import { ToggleButton } from 'primereact/togglebutton';
import { InputSwitch } from 'primereact/inputswitch';

interface CardData {
    id: string;
    cardNumber: string;
    user: string;
    spend: string;
    limitUsage: string;
    expiresIn: string;
    status: string;
    cardType: string;
}

interface Cardholder {
    id: string;
    name: string;
    email: string;
    cards: number;
    spend: string;
    status: string;
}

interface Transaction {
    id: string;
    date: string;
    status: string;
    cardNickname: string;
    cardholderOrContact: string;
    cardNumber: string;
    description: string;
    amount: string;
}

const CardsPage = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [summarySubTab, setSummarySubTab] = useState('cards');
    const [selectedCards, setSelectedCards] = useState<string[]>([]);
    const [searchValue, setSearchValue] = useState('');
    const [cardTypeFilter, setCardTypeFilter] = useState('');
    const [spendFilter, setSpendFilter] = useState('');
    const [limitUsageFilter, setLimitUsageFilter] = useState('');
    const [cardStatusesSelected, setCardStatusesSelected] = useState(3);
    const [businessName, setBusinessName] = useState('CONNECTING MATRIX LLC');
    const [automaticConversions, setAutomaticConversions] = useState(true);
    const [primaryCurrency, setPrimaryCurrency] = useState('USD');

    const cards: CardData[] = [
        {
            id: '1',
            cardNumber: '•••• 6178',
            user: 'Muhammad...',
            spend: '0.00 USD',
            limitUsage: '-',
            expiresIn: '32 months',
            status: 'In use',
            cardType: 'Company card'
        }
    ];

    const cardholders: Cardholder[] = [
        {
            id: '1',
            name: 'Muhammad Abeer',
            email: '<EMAIL>',
            cards: 0,
            spend: '-',
            status: 'Info required'
        }
    ];

    const transactions: Transaction[] = [
        {
            id: '1',
            date: '2026-05-15',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '2',
            date: '2026-05-12',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '3',
            date: '2026-05-11',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '4',
            date: '2026-04-20',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '5',
            date: '2026-04-15',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '6',
            date: '2026-04-12',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '7',
            date: '2026-04-11',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'GetworkHolic.Com, 9197866696769, IND\nInsufficient balance',
            amount: '-9.98 USD'
        },
        {
            id: '8',
            date: '2026-04-04',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'Linked In Srl P 313946743, 8656635663, USA\nCard\'s spend limit exceeded',
            amount: '-53.29 USD'
        },
        {
            id: '9',
            date: '2026-04-01',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'Linked In Srl P 313946743, 8656635663, USA\nCard\'s spend limit exceeded',
            amount: '-53.29 USD'
        },
        {
            id: '10',
            date: '2026-03-28',
            status: 'Declined',
            cardNickname: 'Muhammad Abeer',
            cardholderOrContact: 'Muhammad Abeer',
            cardNumber: '...6178',
            description: 'Linked In Srl P *********, 8656635663, USA\nCard\'s spend limit exceeded',
            amount: '-53.29 USD'
        }
    ];

    const cardTypeOptions = [
        { label: 'Card type', value: '' },
        { label: 'Company card', value: 'Company card' },
        { label: 'Employee card', value: 'Employee card' }
    ];

    const spendOptions = [
        { label: 'Monthly spend in USD', value: '' },
        { label: '0-100 USD', value: '0-100' },
        { label: '100-500 USD', value: '100-500' },
        { label: '500+ USD', value: '500+' }
    ];

    const limitUsageOptions = [
        { label: 'Limit usage', value: '' },
        { label: 'Monthly, USD', value: 'Monthly, USD' },
        { label: 'Daily, USD', value: 'Daily, USD' }
    ];

    const currencies = [
        { code: 'AUD', flag: '🇦🇺' },
        { code: 'CAD', flag: '🇨🇦' },
        { code: 'CHF', flag: '🇨🇭' },
        { code: 'EUR', flag: '🇪🇺' },
        { code: 'GBP', flag: '🇬🇧' },
        { code: 'HKD', flag: '🇭🇰' },
        { code: 'JPY', flag: '🇯🇵' },
        { code: 'NZD', flag: '🇳🇿' },
        { code: 'SGD', flag: '🇸🇬' },
        { code: 'USD', flag: '🇺🇸' }
    ];

    const renderSummaryTab = () => (
        <div className="p-4">
            {/* Header */}
            <div className="flex align-items-center justify-content-between mb-4">
                <div className="flex gap-4">
                    <Button
                        label="Cards"
                        className={`p-button-text ${summarySubTab === 'cards' ? 'font-bold border-bottom-2 border-primary' : 'text-500'}`}
                        onClick={() => setSummarySubTab('cards')}
                    />
                    <Button
                        label="Cardholders"
                        className={`p-button-text ${summarySubTab === 'cardholders' ? 'font-bold border-bottom-2 border-primary' : 'text-500'}`}
                        onClick={() => setSummarySubTab('cardholders')}
                    />
                </div>
                <div className="flex align-items-center gap-3">
                    <div className="flex align-items-center gap-2">
                        <span className="text-sm">View:</span>
                        <Dropdown value="Monthly" options={[{label: 'Monthly', value: 'Monthly'}]} className="w-10rem" />
                    </div>
                    <div className="flex align-items-center gap-2">
                        <span className="text-sm">Spend in</span>
                        <Dropdown value="USD" options={[{label: 'USD', value: 'USD'}]} className="w-8rem" />
                    </div>
                    {/* <span className="text-sm"><EMAIL></span> */}
                    {/* <Avatar icon="pi pi-user" className="bg-gray-300" /> */}
                    <Button
                        label="Create card"
                        className="bg-blue-500 border-none"
                    />
                </div>
            </div>

            {summarySubTab === 'cards' ? renderCardsContent() : renderCardholdersContent()}
        </div>
    );

    const renderCardsContent = () => (
        <>
            {/* Summary Cards */}
            <div className="grid mb-4">
                <div className="col-12 md:col-4">
                    <Card className="text-center">
                        <div className="text-2xl font-bold text-primary mb-2">1</div>
                        <div className="text-sm text-500 mb-1">All cards</div>
                        <div className="text-sm">Total spend 0.00 USD</div>
                    </Card>
                </div>
                <div className="col-12 md:col-4">
                    <Card className="text-center">
                        <div className="text-2xl font-bold text-primary mb-2">1</div>
                        <div className="text-sm text-500 mb-1">Company cards</div>
                        <div className="text-sm">Total spend 0.00 USD</div>
                    </Card>
                </div>
                <div className="col-12 md:col-4">
                    <Card className="text-center">
                        <div className="text-2xl font-bold text-primary mb-2">0</div>
                        <div className="text-sm text-500 mb-1">Employee cards</div>
                        <div className="text-sm">Total spend 0.00 USD</div>
                    </Card>
                </div>
            </div>

            {/* Search and Filters */}
            <div className="flex align-items-center gap-3 mb-4">
                <div className="flex align-items-center gap-2 flex-1">
                    {/* <i className="pi pi-search text-500"></i> */}
                    <InputText
                        placeholder="Search via the card nickname, last 4 digits of the card number, cardholder or card contact"
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        className="flex-1"
                    />
                </div>
                <Dropdown
                    value={cardTypeFilter}
                    options={cardTypeOptions}
                    onChange={(e) => setCardTypeFilter(e.value)}
                    placeholder="Card type"
                    className="w-8rem"
                />
                <Dropdown
                    value={spendFilter}
                    options={spendOptions}
                    onChange={(e) => setSpendFilter(e.value)}
                    placeholder="Monthly spend in USD"
                    className="w-12rem"
                />
                <Dropdown
                    value={limitUsageFilter}
                    options={limitUsageOptions}
                    onChange={(e) => setLimitUsageFilter(e.value)}
                    placeholder="Limit usage"
                    className="w-8rem"
                />
                <div className="flex align-items-center gap-2 bg-indigo-100 px-3 py-2 border-round">
                    <span className="text-sm">{cardStatusesSelected} card statuses selected</span>
                    <Button icon="pi pi-times" className="p-button-text p-button-plain text-xs" />
                </div>
            </div>

            {/* Cards Table */}
            <DataTable
                value={cards}
                className=""
                selection={selectedCards}
                onSelectionChange={(e) => setSelectedCards(e.value)}
                dataKey="id"
            >
                <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
                <Column
                    field="cardNumber"
                    header="Cards"
                    body={(rowData) => (
                        <div className="flex align-items-center gap-2">
                            <div className="w-2rem h-1rem bg-gray-300 border-round"></div>
                            <span className="text-sm">{rowData.cardNumber}</span>
                        </div>
                    )}
                />
                <Column
                    field="cardNumber"
                    header="Card No."
                    body={(rowData) => (
                        <span className="text-sm">{rowData.cardNumber}</span>
                    )}
                />
                <Column
                    field="user"
                    header="Users"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.user}</span>
                    )}
                />
                <Column
                    field="spend"
                    header="Spend"
                    body={(rowData) => (
                        <div>
                            <div className="text-sm font-bold">{rowData.spend}</div>
                            <div className="text-xs text-500">Monthly, USD</div>
                        </div>
                    )}
                />
                <Column
                    field="limitUsage"
                    header="Limit Usage"
                    body={(rowData) => (
                        <div>
                            <div className="text-sm">{rowData.limitUsage}</div>
                            <div className="text-xs text-500">Monthly, USD</div>
                        </div>
                    )}
                />
                <Column
                    field="expiresIn"
                    header="Expires In"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.expiresIn}</span>
                    )}
                />
                <Column
                    field="status"
                    header="Status"
                    body={(rowData) => (
                        <Badge
                            value={rowData.status}
                            severity="success"
                            className="text-xs"
                        />
                    )}
                />
                <Column
                    header="Tasks"
                    body={() => (
                        <Button
                            icon="pi pi-ellipsis-h"
                            className="p-button-text p-button-plain"
                        />
                    )}
                />
            </DataTable>

            {/* Pagination */}
            <div className="flex align-items-center justify-content-between mt-4">
                <div className="flex align-items-center gap-2">
                    <Button icon="pi pi-angle-double-left" className="p-button-text" />
                    <Button icon="pi pi-angle-left" className="p-button-text" />
                    <Button label="1" className="p-button-text font-bold" />
                    <Button icon="pi pi-angle-right" className="p-button-text" />
                    <Button icon="pi pi-angle-double-right" className="p-button-text" />
                </div>
                <div className="flex align-items-center gap-2">
                    <span className="text-sm">Rows per page:</span>
                    <Dropdown value={20} options={[{label: '20', value: 20}]} className="w-5rem" />
                    <span className="text-sm">1 - 1 of 1</span>
                </div>
            </div>
        </>
    );

    const renderCardholdersContent = () => (
        <>
            {/* Search and Filters */}
            <div className="flex align-items-center gap-3 mb-4">
                <div className="flex align-items-center gap-2 flex-1">
                    {/* <i className="pi pi-search text-500"></i> */}
                    <InputText
                        placeholder="Search via the cardholder name"
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        className="flex-1"
                    />
                </div>
                <Dropdown
                    value=""
                    options={[{label: 'Cardholder spend', value: ''}]}
                    placeholder="Cardholder spend"
                    className="w-10rem"
                />
                <Dropdown
                    value=""
                    options={[{label: 'Cardholder status', value: ''}]}
                    placeholder="Cardholder status"
                    className="w-10rem"
                />
                <div className="flex align-items-center gap-2">
                    <Checkbox inputId="physical-card" />
                    <label htmlFor="physical-card" className="text-sm">Has physical card</label>
                </div>
            </div>

            {/* Cardholders Table */}
            <DataTable
                value={cardholders}
                className=""
            >
                <Column
                    field="name"
                    header="Cardholders"
                    body={(rowData) => (
                        <div className="flex align-items-center gap-3">
                            <Avatar icon="pi pi-user" className="bg-gray-300" />
                            <div>
                                <div className="font-bold text-sm">{rowData.name}</div>
                                <div className="text-xs text-500">{rowData.email}</div>
                            </div>
                        </div>
                    )}
                />
                <Column
                    field="cards"
                    header="Cards"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.cards} cards</span>
                    )}
                />
                <Column
                    field="spend"
                    header="Spend"
                    body={(rowData) => (
                        <div>
                            <div className="text-sm">{rowData.spend}</div>
                            <div className="text-xs text-500">Monthly, USD</div>
                        </div>
                    )}
                />
                <Column
                    field="status"
                    header="Status"
                    body={(rowData) => (
                        <Badge
                            value={rowData.status}
                            severity="warning"
                            className="text-xs"
                        />
                    )}
                />
                <Column
                    body={() => (
                        <Button
                            icon="pi pi-ellipsis-h"
                            className="p-button-text p-button-plain"
                        />
                    )}
                />
            </DataTable>

            {/* Pagination */}
            <div className="flex align-items-center justify-content-between mt-4">
                <div className="flex align-items-center gap-2">
                    <Button icon="pi pi-angle-double-left" className="p-button-text" />
                    <Button icon="pi pi-angle-left" className="p-button-text" />
                    <Button label="1" className="p-button-text font-bold" />
                    <Button icon="pi pi-angle-right" className="p-button-text" />
                    <Button icon="pi pi-angle-double-right" className="p-button-text" />
                </div>
                <div className="flex align-items-center gap-2">
                    <span className="text-sm">Rows per page:</span>
                    <Dropdown value={25} options={[{label: '25', value: 25}]} className="w-5rem" />
                    <span className="text-sm">1 - 1 of 1</span>
                </div>
            </div>
        </>
    );

    const renderTransactionsTab = () => (
        <div className="p-4">
            {/* Header */}
            <div className="flex align-items-center justify-content-between mb-4">
                <div className="flex align-items-center gap-2">
                    {/* <span className="text-sm"><EMAIL></span> */}
                    {/* <Avatar icon="pi pi-user" className="bg-gray-300" /> */}
                </div>
            </div>

            {/* Filters */}
            <div className="flex align-items-center gap-3 mb-4">
                <div className="flex align-items-center gap-2">
                    <i className="pi pi-calendar text-500"></i>
                    <span className="text-sm">Date from</span>
                    <Calendar placeholder="Date from" className="w-8rem" />
                </div>
                <div className="flex align-items-center gap-2">
                    <span className="text-sm">Date to</span>
                    <Calendar placeholder="Date to" className="w-8rem" />
                </div>
                <div className="flex align-items-center gap-2">
                    <i className="pi pi-tag text-500"></i>
                    <Dropdown
                        value=""
                        options={[{label: 'Status', value: ''}]}
                        placeholder="Status"
                        className="w-8rem"
                    />
                </div>
                <div className="flex align-items-center gap-2">
                    <i className="pi pi-dollar text-500"></i>
                    <Dropdown
                        value=""
                        options={[{label: 'Currency', value: ''}]}
                        placeholder="Currency"
                        className="w-8rem"
                    />
                </div>
            </div>

            {/* Transactions Table */}
            <DataTable
                value={transactions}
                className=""
                paginator
                rows={10}
                rowsPerPageOptions={[10, 25, 50]}
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
                currentPageReportTemplate="1 - 10 of 26"
            >
                <Column
                    field="date"
                    header="Date"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.date}</span>
                    )}
                />
                <Column
                    field="status"
                    header="Status"
                    body={(rowData) => (
                        <Badge
                            value={rowData.status}
                            severity="danger"
                            className="text-xs"
                        />
                    )}
                />
                <Column
                    field="cardNickname"
                    header="Card Nickname"
                    body={(rowData) => (
                        <div>
                            <div className="font-bold text-sm">{rowData.cardNickname}</div>
                            <div className="text-xs text-500">Company card</div>
                        </div>
                    )}
                />
                <Column
                    field="cardholderOrContact"
                    header="Cardholder Or Contact"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.cardholderOrContact}</span>
                    )}
                />
                <Column
                    field="cardNumber"
                    header="Card Number"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.cardNumber}</span>
                    )}
                />
                <Column
                    field="description"
                    header="Description"
                    body={(rowData) => (
                        <div className="text-sm" style={{ maxWidth: '200px' }}>
                            {rowData.description.split('\n').map((line, index) => (
                                <div key={index} className={index > 0 ? 'text-500' : ''}>{line}</div>
                            ))}
                        </div>
                    )}
                />
                <Column
                    field="amount"
                    header="Amount"
                    body={(rowData) => (
                        <span className="text-sm font-bold">{rowData.amount}</span>
                    )}
                />
            </DataTable>
        </div>
    );

    const renderSettingsTab = () => (
        <div className="p-4">
            {/* Header */}
            {/* <div className="flex align-items-center justify-content-end mb-6">
                <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div>
            </div> */}

            {/* Business name on cards */}
            <div className="mb-4">
                <h2 className="text-2xl font-bold mb-4">Business name on cards</h2>

                <div className="grid">
                    <div className="col-12 md:col-4">
                        {/* Card Preview */}
                        <div className="bg-gray-900 text-white p-4 border-round-lg mb-4" style={{ aspectRatio: '1.6/1' }}>
                            <div className="flex flex-column h-full justify-content-between">
                                <div>
                                    <div className="bg-gray-600 h-1rem mb-3 border-round" style={{ width: '60%' }}></div>
                                    <div className="bg-gray-600 h-0-5rem border-round" style={{ width: '40%' }}></div>
                                </div>
                                <div>
                                    <div className="text-orange-400 text-sm font-bold mb-1">{businessName}</div>
                                    <div className="text-xs text-gray-400">MUHAMMAD ABEER</div>
                                    <div className="text-xs text-gray-400">•••• •••• •••• 6178</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="col-12 md:col-8">
                        <p className="text-500 mb-4">
                            You can specify a business name to print on the back of the physical cards. This will be effective for any physical cards issued in the future.
                        </p>

                        <div className="mb-4">
                            <label className="block text-sm font-bold mb-2">Print business name on physical cards</label>
                            <div className="flex align-items-center gap-2">
                                <InputText
                                    value={businessName}
                                    onChange={(e) => setBusinessName(e.target.value)}
                                    className="flex-1"
                                />
                                <Button
                                    label="Edit"
                                    icon="pi pi-pencil"
                                    className="p-button-text text-primary"
                                />
                            </div>
                            <small className="text-500">Your business legal name is used by default.</small>
                        </div>
                    </div>
                </div>
            </div>

            {/* Funding */}
            <div>
                <h2 className="text-2xl font-bold mb-4">Funding</h2>

                <p className="text-500 mb-4">
                    Card transactions can only be funded using one of the ten currencies listed below. If spending in an
                    alternative currency (not listed below), your selected primary currency will be used.
                </p>

                <div className="mb-4">
                    <label className="block text-sm font-bold mb-3">Select primary currency</label>
                    <div className="grid">
                        {currencies.map((currency) => (
                            <div key={currency.code} className="col-6 md:col-2 mb-3">
                                <div
                                    className={`border-2 border-round p-3 text-center cursor-pointer transition-colors ${
                                        primaryCurrency === currency.code
                                            ? 'border-indigo-500 bg-indigo-50'
                                            : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                    onClick={() => setPrimaryCurrency(currency.code)}
                                >
                                    <div className="text-2xl mb-1">{currency.flag}</div>
                                    <div className="text-sm font-bold">{currency.code}</div>
                                    {primaryCurrency === currency.code && (
                                        <div className="text-xs text-indigo-600 mt-1">Primary currency</div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="flex align-items-start gap-3 mb-4">
                    <InputSwitch
                        checked={automaticConversions}
                        onChange={(e) => setAutomaticConversions(e.value)}
                    />
                    <div>
                        <label className="font-bold text-900 cursor-pointer">Automatic conversions</label>
                        <p className="text-500 mt-2 mb-0">
                            With <a href="#" className="text-primary">automatic conversions</a> on, we'll use your primary currency to fund the entire transaction (at your
                            current Airwallex rates) when you have insufficient funds in one of the 10 currencies above.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50">
            <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                <TabPanel header="Summary">
                    {renderSummaryTab()}
                </TabPanel>
                <TabPanel header="Transactions">
                    {renderTransactionsTab()}
                </TabPanel>
                <TabPanel header="Settings">
                    {renderSettingsTab()}
                </TabPanel>
            </TabView>
        </div>
    );
};

export default CardsPage;
