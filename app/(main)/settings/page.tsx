'use client';
import React from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';

const SettingsPage = () => {
    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="flex align-items-center justify-content-between px-4 surface-border">
                <div>
                    <h1 className="text-2xl font-bold text-900 m-0">Settings</h1>
                    <div className="w-3rem h-0-25rem bg-orange-500 mt-2"></div>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            <div className="p-4">
                {/* Organisation Header */}
                <div className="flex align-items-center justify-content-between mb-4">
                    <div>
                        <div className="text-sm text-500 mb-1">Organisation</div>
                        <h2 className="text-2xl font-bold text-900 m-0">CONNECTING MATRIX LLC</h2>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            label="Create entity"
                            icon="pi pi-plus"
                            className="p-button-outlined text-primary border-primary"
                        />
                        <Button
                            label="Create account"
                            icon="pi pi-plus"
                            className="p-button-outlined text-primary border-primary"
                        />
                    </div>
                </div>

                {/* Organisation Information Card */}
                <Card className="mb-4">
                    <h3 className="text-lg font-bold text-900 mb-2">Organisation information</h3>

                    <div className="flex align-items-center justify-content-between py-3">
                        <span className="text-600">Organisation name</span>
                        <div className="flex align-items-center gap-2">
                            <span className="text-900 font-medium">CONNECTING MATRIX LLC</span>
                            <Button
                                icon="pi pi-pencil"
                                className="p-button-text p-button-plain text-500"
                                style={{ padding: '0.25rem' }}
                            />
                        </div>
                    </div>
                </Card>

                {/* Entities Section */}
                <div className="mb-4">
                    <h3 className="text-lg font-bold text-900 mb-3">Entities</h3>

                    <Card>
                        <div className="flex align-items-center justify-content-between mb-4">
                            <div className="flex align-items-center gap-3">
                                {/* <span className="flag-icon text-lg">🇺🇸</span> */}
                                <div className='flex align-items-center'>
                                    <div className="font-bold text-900">CONNECTING MATRIX LLC</div>
                                    <Badge value="Active" severity="success" className="text-xs ml-2" />
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="text-sm text-500">1 account</div>
                                {/* <Button
                                    icon="pi pi-angle-down"
                                    className="p-button-text p-button-plain text-500"
                                /> */}
                            </div>
                        </div>

                        <div className="grid">
                            {/* Business Information */}
                            <div className="col-12">
                                <h4 className="text-md font-bold text-900 mb-3">Business information</h4>

                                <div className="space-y-3">
                                    <div className="flex justify-content-between py-2">
                                        <span className="text-600">Legal entity name</span>
                                        <span className="text-900">CONNECTING MATRIX LLC</span>
                                    </div>

                                    <div className="flex justify-content-between py-2">
                                        <span className="text-600">Entity registration location</span>
                                        <div className="flex align-items-center gap-2">
                                            <span className="flag-icon">🇺🇸</span>
                                            <span className="text-900">United States of America</span>
                                        </div>
                                    </div>

                                    <div className="flex justify-content-between py-2">
                                        <span className="text-600">Legal entity form</span>
                                        <span className="text-900">Company</span>
                                    </div>

                                    <div className="flex justify-content-between py-2">
                                        <span className="text-600">Registered office address</span>
                                        <div className="text-right">
                                            <div className="text-900">5900 BALCONES DR 18462</div>
                                            <div className="text-900">AUSTIN, TX 78731 Austin TX 78731</div>
                                        </div>
                                    </div>
                                </div>

                                <h4 className="text-md font-bold text-900 mt-6 mb-3">Business persons</h4>

                                <div className="flex justify-content-between py-2">
                                    <span className="text-600">Ultimate beneficial owner</span>
                                    <div className="text-right">
                                        <div className="text-900">Maham Fatima Mir</div>
                                        <div className="text-900">Muhammad Abeer</div>
                                    </div>
                                </div>

                                <h4 className="text-md font-bold text-900 mb-3">Accounts</h4>
                                <p className="text-600 mb-3">You have access to the following accounts</p>

                                <div className="flex align-items-center justify-content-between p-3 border-1 surface-border border-round">
                                    <div className="flex align-items-center gap-2">
                                        <i className="pi pi-building text-500"></i>
                                        <span className="text-900">CONNECTING MATRIX LLC</span>
                                    </div>
                                    <Button
                                        icon="pi pi-angle-right"
                                        className="p-button-text p-button-plain text-500"
                                    />
                                </div>

                            </div>

                        </div>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default SettingsPage;
