'use client';
import React from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';

const RequestsPage = () => {
    return (
        <div className="grid">
            <div className="col-12">
                <Card title="Requests" className="mb-4">
                    <div className="flex align-items-center justify-content-center" style={{ minHeight: '300px' }}>
                        <div className="text-center">
                            <i className="pi pi-send text-6xl text-500 mb-3"></i>
                            <h3 className="text-500">Payment Requests</h3>
                            <p className="text-500 mb-4">Create and manage payment requests</p>
                            <Button label="New Request" icon="pi pi-plus" className="p-button-primary" />
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default RequestsPage;
