'use client';
import React, { useState } from 'react';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Avatar } from 'primereact/avatar';

interface Recipient {
    id: string;
    name: string;
    nickname?: string;
    transferMethod: string;
    country: string;
    accountDetails: string;
    accountType: string;
}

const ContactsPage = () => {
    const [searchValue, setSearchValue] = useState('');

    const recipients: Recipient[] = [
        {
            id: '1',
            name: '<PERSON><PERSON> Shahid',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '2',
            name: 'Misbah Tahir',
            nickname: 'Farah Yaqoob',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '3',
            name: '<PERSON><PERSON><PERSON>',
            nickname: '<PERSON><PERSON>',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '4',
            name: 'Abdul Wahaab',
            nickname: 'Abdul',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '5',
            name: 'Mir Ghulam Murtaza',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '6',
            name: 'Mian Haider Mutih',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '7',
            name: 'Syeda Mafia Shah',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '8',
            name: 'Muhammad Memoon',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '9',
            name: 'Zain Ul Abideen',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        },
        {
            id: '10',
            name: 'Mujeib Muneeb',
            transferMethod: 'Local',
            country: 'PKR Pakistan',
            accountDetails: '***********************',
            accountType: 'IBAN'
        }
    ];

    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    };

    const getRandomColor = (name: string) => {
        const colors = [
            'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 
            'bg-pink-500', 'bg-teal-500', 'bg-indigo-500', 'bg-red-500'
        ];
        const index = name.length % colors.length;
        return colors[index];
    };

    return (
        <div className="min-h-screen bg-gray-50 p-4">
            {/* Header */}
            <div className="flex justify-content-between align-items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold m-0">Contacts</h1>
                </div>
                {/* <div className="flex align-items-center gap-3">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                    <Button 
                        label="New recipient" 
                        className="bg-indigo-600 border-none"
                    />
                </div> */}
            </div>

            {/* Search */}
            <div className="flex align-items-center gap-2 mb-4">
                {/* <i className="pi pi-search text-500"></i> */}
                <InputText 
                    placeholder="Search recipient name, account number, or nickname"
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="flex-1"
                />
            </div>

            {/* Recipients Table */}
            <DataTable 
                value={recipients} 
                className="bg-white"
                paginator
                rows={10}
                rowsPerPageOptions={[10, 25, 50]}
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
                currentPageReportTemplate="1 - 10 of 11"
            >
                <Column 
                    field="name" 
                    header="Recipient Name & Nickname"
                    body={(rowData) => (
                        <div className="flex align-items-center gap-3">
                            <div className={`w-3rem h-3rem border-circle flex align-items-center justify-content-center text-white font-bold ${getRandomColor(rowData.name)}`}>
                                {getInitials(rowData.name)}
                            </div>
                            <div>
                                <div className="font-bold text-sm">{rowData.name}</div>
                                {rowData.nickname && (
                                    <div className="text-xs text-500">{rowData.nickname}</div>
                                )}
                            </div>
                        </div>
                    )}
                />
                <Column 
                    field="transferMethod" 
                    header="Transfer Method"
                    body={(rowData) => (
                        <div>
                            <div className="font-bold text-sm">{rowData.transferMethod}</div>
                            <div className="text-xs text-500">{rowData.country}</div>
                        </div>
                    )}
                />
                <Column 
                    field="accountDetails" 
                    header="Account Details"
                    body={(rowData) => (
                        <div>
                            <div className="font-bold text-sm">{rowData.accountDetails}</div>
                            <div className="text-xs text-500">{rowData.accountType}</div>
                        </div>
                    )}
                />
                <Column 
                    body={(rowData) => (
                        <Button 
                            icon="pi pi-ellipsis-h" 
                            className="p-button-text p-button-plain"
                        />
                    )}
                    style={{ width: '4rem' }}
                />
            </DataTable>
        </div>
    );
};

export default ContactsPage;
