'use client';
import React from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';

const BillsPage = () => {
    return (
        <div className="grid">
            <div className="col-12">
                <Card title="Bills" className="mb-4">
                    <div className="flex align-items-center justify-content-center" style={{ minHeight: '300px' }}>
                        <div className="text-center">
                            <i className="pi pi-file-o text-6xl text-500 mb-3"></i>
                            <h3 className="text-500">Bill Management</h3>
                            <p className="text-500 mb-4">Upload, approve, and pay your bills</p>
                            <Button label="Upload Bill" icon="pi pi-upload" className="p-button-primary" />
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default BillsPage;
