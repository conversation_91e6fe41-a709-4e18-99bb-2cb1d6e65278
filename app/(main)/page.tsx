/* eslint-disable @next/next/no-img-element */
'use client';
import { But<PERSON> } from 'primereact/button';
import { Chart } from 'primereact/chart';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { Badge } from 'primereact/badge';
import React, { useContext, useEffect, useState } from 'react';
import { LayoutContext } from '../../layout/context/layoutcontext';
import { ChartData, ChartOptions } from 'chart.js';

// Sample data for the financial dashboard
const balanceChartData: ChartData = {
    labels: ['May 31, 2025', 'Jun 05, 2025'],
    datasets: [
        {
            data: [0, 800, 600, 400, 200, 0],
            fill: true,
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            borderColor: '#6366f1',
            tension: 0.4,
            pointRadius: 0,
        }
    ]
};

const transactionChartData: ChartData = {
    labels: ['May 11, 2025', 'Jun 04, 2025', 'Jun 05, 2025'],
    datasets: [
        {
            label: 'Total money in',
            data: [0, 796.87, 796.87],
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            borderColor: '#6366f1',
            tension: 0.4,
            fill: true,
        },
        {
            label: 'Total money out',
            data: [0, -1764.13, -1764.13],
            backgroundColor: 'rgba(168, 85, 247, 0.1)',
            borderColor: '#a855f7',
            tension: 0.4,
            fill: true,
        }
    ]
};

const currencies = [
    { label: 'USD', value: 'USD' },
    { label: 'EUR', value: 'EUR' },
    { label: 'GBP', value: 'GBP' },
    { label: 'AUD', value: 'AUD' },
    { label: 'CAD', value: 'CAD' }
];

const topCurrencies = [
    { code: 'AUD', flag: '🇦🇺', amount: '0.00 AUD' },
    { code: 'CAD', flag: '🇨🇦', amount: '0.00 CAD' },
    { code: 'EUR', flag: '🇪🇺', amount: '0.00 EUR' },
    { code: 'GBP', flag: '🇬🇧', amount: '0.00 GBP' }
];

const globalAccounts = [
    {
        name: 'CONNECTING MATRIX LLC',
        country: '🇺🇸',
        accountNumber: 'ACH Routing No. ********* • Acc No. *********',
        type: 'US Account'
    },
    {
        name: 'CONNECTING MATRIX LLC',
        country: '🇦🇪',
        accountNumber: 'IBAN ********************',
        type: 'UAE Account'
    },
    {
        name: 'CONNECTING MATRIX LLC',
        country: '🇨🇳',
        accountNumber: 'Bank code 717 • Branch code 072 • Acc No. *********',
        type: 'China Account'
    }
];

const recentTransactions = [
    {
        date: '2025-06-05',
        product: 'Transfers',
        details: 'Pay MUHAMMAD MEMOON 43,024.44 PKR (OPS Payment)',
        balanceType: 'Cash',
        amount: '+54.53 USD'
    },
    {
        date: '2025-06-04',
        product: 'Transfers',
        details: 'Pay Komal Shahid 72,500.00 PKR (Salary)',
        balanceType: 'Cash',
        amount: '-260.40 USD'
    },
    {
        date: '2025-06-04',
        product: 'Transfers',
        details: 'Pay MUHAMMAD MEMOON 40,000.00 PKR (Salary)',
        balanceType: 'Cash',
        amount: '-143.67 USD'
    },
    {
        date: '2025-06-04',
        product: 'Transfers',
        details: 'Pay MISBAH TAHIRA 35,000.00 PKR (Salary)',
        balanceType: 'Cash',
        amount: '-125.71 USD'
    },
    {
        date: '2025-06-04',
        product: 'Transfers',
        details: 'Pay Zain UI Abideen 143,000.00 PKR (Salary)',
        balanceType: 'Cash',
        amount: '-513.61 USD'
    },
    {
        date: '2025-06-04',
        product: 'Transfers',
        details: 'Pay Mujeb Muneeb 200,000.00 PKR (Salary)',
        balanceType: 'Cash',
        amount: '-718.34 USD'
    },
    {
        date: '2025-06-04',
        product: 'Global Accounts',
        details: 'Fee for deposit from WISE US INC (MUHAMMAD A From Muhammad Abeer Via WISE)',
        balanceType: 'Cash',
        amount: '-2.40 USD'
    },
    {
        date: '2025-06-04',
        product: 'Global Accounts',
        details: 'Deposit from WISE US INC (MUHAMMAD A From Muhammad Abeer Via WISE) | CONNECTING MATRIX LLC, *********',
        balanceType: 'Cash',
        amount: '+798.87 USD'
    }
];

const Dashboard = () => {
    const [selectedCurrency, setSelectedCurrency] = useState('USD');
    const [chartOptions, setChartOptions] = useState<ChartOptions>({});
    const { layoutConfig } = useContext(LayoutContext);

    const applyLightTheme = () => {
        const options: ChartOptions = {
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    display: false
                }
            },
            maintainAspectRatio: false,
            responsive: true
        };
        setChartOptions(options);
    };

    const applyDarkTheme = () => {
        const options: ChartOptions = {
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    display: false
                }
            },
            maintainAspectRatio: false,
            responsive: true
        };
        setChartOptions(options);
    };

    useEffect(() => {
        if (layoutConfig.colorScheme === 'light') {
            applyLightTheme();
        } else {
            applyDarkTheme();
        }
    }, [layoutConfig.colorScheme]);

    const formatCurrency = (value: string) => {
        const isNegative = value.startsWith('-');
        return (
            <span className={isNegative ? 'text-red-500' : 'text-green-500'}>
                {value}
            </span>
        );
    };

    const productBodyTemplate = (rowData: any) => {
        return (
            <div className="flex align-items-center">
                <i className="pi pi-arrow-right-arrow-left mr-2"></i>
                <span>{rowData.product}</span>
            </div>
        );
    };

    return (
        <div className="grid">
            {/* Header Actions */}
            <div className="col-12">
                <div className="flex gap-3 mb-4">
                    <Button label="Add funds" icon="pi pi-plus" className="p-button-outlined" />
                    <Button label="Create transfer" icon="pi pi-send" className="p-button-outlined" />
                    <Button label="Create card" icon="pi pi-credit-card" className="p-button-outlined" />
                </div>
            </div>

            {/* Cash Balances */}
            <div className="col-12 lg:col-8">
                <div className="card">
                    <div className="flex justify-content-between align-items-center mb-4">
                        <h5>Cash Balances</h5>
                        <div className="flex align-items-center gap-3">
                            <span className="text-sm text-500">Top currency balances</span>
                            <Button label="View all 5" className="p-button-text p-button-sm pr-0" />
                        </div>
                    </div>
                    
                    <div className="flex align-items-center gap-3 mb-4">
                        <div className="text-4xl font-bold">0.00</div>
                        <Dropdown 
                            value={selectedCurrency} 
                            options={currencies} 
                            onChange={(e) => setSelectedCurrency(e.value)} 
                            className="w-auto"
                        />
                        <i className="pi pi-info-circle text-500"></i>
                    </div>
                    
                    <div className="text-sm text-500 mb-4">Balance updated on Jun 10, 2025, 20:38</div>
                    
                    <div className="mb-4">
                        <Chart type="line" data={balanceChartData} options={chartOptions} />
                    </div>
                </div>
            </div>

            {/* Top Currency Balances & Pending Approvals */}
            <div className="col-12 lg:col-4">
                <div className="card mb-3">
                    <div className="flex justify-content-between align-items-center mb-3">
                        <span className="text-sm font-medium">Top currency balances</span>
                        <Button label="View all 5" className="p-button-text p-button-sm pr-0" />
                    </div>
                    
                    {topCurrencies.map((currency, index) => (
                        <div key={index} className="flex justify-content-between align-items-center py-2">
                            <div className="flex align-items-center gap-2">
                                <span className="text-lg">{currency.flag}</span>
                                <span className="font-medium">{currency.code}</span>
                            </div>
                            <span className="text-500">{currency.amount}</span>
                        </div>
                    ))}
                </div>

                <div className="card">
                    <h6 className="mb-3">Pending approvals</h6>
                    <div className="flex justify-content-between align-items-center py-2">
                        <span>Transfers</span>
                        <Badge value="0" />
                    </div>
                    <div className="flex justify-content-between align-items-center py-2">
                        <span>Batch transfers</span>
                        <Badge value="0" />
                    </div>
                </div>
            </div>

            {/* Bill Payments */}
            <div className="col-12 lg:col-6">
                <div className="card">
                    <div className="flex align-items-center gap-2 mb-3">
                        <Badge value="Recommended" severity="info" className="text-xs" />
                    </div>
                    <h5 className="mb-3">Streamline your bill payments</h5>
                    <p className="text-500 mb-4">Upload, approve, pay, and reconcile your domestic and international bills all within your Airwallex account</p>

                    <div className="flex align-items-center gap-3 mb-4">
                        <Button label="Explore bill pay" icon="pi pi-arrow-right" className="p-button-text" />
                    </div>

                    <div className="border-1 surface-border border-round p-3">
                        <div className="flex align-items-center gap-3">
                            <div className="bg-orange-100 border-round p-2">
                                <span className="text-orange-500 font-bold">Apex Packaging</span>
                            </div>
                            <div className="flex-1">
                                <div className="font-medium">INVOICE</div>
                                <div className="text-sm text-500">Invoice details...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Global Accounts */}
            <div className="col-12 lg:col-6">
                <div className="card">
                    <div className="flex justify-content-between align-items-center mb-4">
                        <h5>Global Accounts</h5>
                        <div className="flex gap-2">
                            <Button label="View all 4" className="p-button-text p-button-sm" />
                            <Button label="Create Global Account" icon="pi pi-plus" className="p-button-sm" />
                        </div>
                    </div>

                    {globalAccounts.map((account, index) => (
                        <div key={index} className="flex justify-content-between align-items-center py-3 border-bottom-1 surface-border">
                            <div className="flex align-items-center gap-3">
                                <span className="text-2xl">{account.country}</span>
                                <div>
                                    <div className="font-medium">{account.name}</div>
                                    <div className="text-sm text-500">{account.accountNumber}</div>
                                </div>
                            </div>
                            <Button icon="pi pi-ellipsis-v" className="p-button-text p-button-plain" />
                        </div>
                    ))}
                </div>
            </div>

            {/* Cards */}
            <div className="col-12 lg:col-6">
                <div className="card">
                    <div className="flex justify-content-between align-items-center mb-4">
                        <h5>Cards</h5>
                        <div className="flex gap-2">
                            <Button label="View all" className="p-button-text p-button-sm" />
                            <Button label="Create card" icon="pi pi-plus" className="p-button-sm" />
                        </div>
                    </div>

                    <div className="flex align-items-center gap-3 p-3 border-1 surface-border border-round">
                        <div className="w-3rem h-2rem bg-primary border-round flex align-items-center justify-content-center">
                            <i className="pi pi-credit-card text-white"></i>
                        </div>
                        <div className="flex-1">
                            <div className="font-medium">Muhammad Abeer</div>
                            <div className="text-sm text-500">Company</div>
                        </div>
                        <div className="text-right">
                            <div className="font-medium">Muhammad Abeer</div>
                            <div className="text-sm text-500">0.00 USD</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Transfers */}
            <div className="col-12 lg:col-6">
                <div className="card">
                    <div className="flex justify-content-between align-items-center mb-4">
                        <h5>Transfers</h5>
                        <div className="flex gap-2">
                            <Button label="View all" className="p-button-text p-button-sm" />
                            <Button label="Create transfer" icon="pi pi-plus" className="p-button-sm" />
                        </div>
                    </div>

                    <div className="grid">
                        <div className="col-6">
                            <div className="text-center">
                                <div className="text-2xl font-bold">6</div>
                                <div className="text-sm text-500">Transfers sent in last 30 days</div>
                                <div className="text-xs text-500">1,016.36 USD</div>
                            </div>
                        </div>
                        <div className="col-6">
                            <div className="text-center">
                                <div className="text-2xl font-bold">0</div>
                                <div className="text-sm text-500">Transfers pending approval</div>
                                <div className="text-xs text-500">0.00 USD</div>
                            </div>
                        </div>
                        <div className="col-6">
                            <div className="text-center">
                                <div className="text-2xl font-bold">0</div>
                                <div className="text-sm text-500">Batch transfers pending approval</div>
                                <div className="text-xs text-500">0.00 USD</div>
                            </div>
                        </div>
                        <div className="col-6">
                            <div className="text-center">
                                <div className="text-2xl font-bold">0</div>
                                <div className="text-sm text-500">Transfers rejected in last 30 days</div>
                                <div className="text-xs text-500">0.00 USD</div>
                            </div>
                        </div>
                    </div>

                    <div className="text-center mt-3">
                        <div className="text-sm text-500">Transfer amount spent 30 days</div>
                    </div>
                </div>
            </div>

            {/* Transactions */}
            <div className="col-12">
                <div className="card">
                    <div className="flex justify-content-between align-items-center mb-4">
                        <h5>Transactions</h5>
                        <div className="flex gap-2">
                            <Button label="View all" className="p-button-text p-button-sm" />
                            <Dropdown
                                value="Product"
                                options={[{label: 'Product', value: 'Product'}]}
                                className="w-auto"
                            />
                        </div>
                    </div>

                    <div className="grid">
                        <div className="col-12">
                            <div className="mb-4">
                                <h6>Total transactions in last 30 days</h6>
                                <div className="text-3xl font-bold mb-2">0.00 USD</div>

                                <div className="h-12rem">
                                    <Chart type="line" data={transactionChartData} options={{
                                        ...chartOptions,
                                        plugins: {
                                            legend: {
                                                display: true,
                                                position: 'bottom'
                                            }
                                        },
                                        scales: {
                                            x: {
                                                display: true,
                                                grid: {
                                                    display: false
                                                }
                                            },
                                            y: {
                                                display: true,
                                                grid: {
                                                    display: true
                                                }
                                            }
                                        }
                                    }} />
                                </div>

                                <div className="grid mt-4">
                                    <div className="col-6">
                                        <div className="text-center p-3 border-1 surface-border border-round">
                                            <div className="text-lg font-bold text-green-500">1,922.03 USD</div>
                                            <div className="text-sm text-500">Total money in for last 30 days</div>
                                        </div>
                                    </div>
                                    <div className="col-6">
                                        <div className="text-center p-3 border-1 surface-border border-round">
                                            <div className="text-lg font-bold text-red-500">-1,922.03 USD</div>
                                            <div className="text-sm text-500">Total money out for last 30 days</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-12">
                            <div className="grid">
                                <div className="col-12 lg:col-6">
                                    <div className="text-center mb-4">
                                        <h6>Total money in for last 30 days</h6>
                                        <div className="text-lg font-bold text-green-500 mb-2">1,922.03 USD</div>
                                        <div className="w-14rem mx-auto">
                                            <Chart type="doughnut" data={{
                                                datasets: [{
                                                    data: [100],
                                                    backgroundColor: ['#6366f1'],
                                                    borderWidth: 0
                                                }]
                                            }} options={{
                                                plugins: {
                                                    legend: {
                                                        display: false
                                                    }
                                                },
                                                maintainAspectRatio: false
                                            }} />
                                        </div>
                                        <div className="text-xs text-500 mt-2">
                                            <span className="inline-flex align-items-center gap-1">
                                                <span className="w-1rem h-1rem bg-primary border-round"></span>
                                                Global Accounts 100.00%
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div className="col-12 lg:col-6">
                                    <div className="text-center">
                                        <h6>Total money out for last 30 days</h6>
                                        <div className="text-lg font-bold text-red-500 mb-2">-1,922.03 USD</div>
                                        <div className="w-14rem mx-auto">
                                            <Chart type="doughnut" data={{
                                                datasets: [{
                                                    data: [99.69, 0.31],
                                                    backgroundColor: ['#a855f7', '#6366f1'],
                                                    borderWidth: 0
                                                }]
                                            }} options={{
                                                plugins: {
                                                    legend: {
                                                        display: false
                                                    }
                                                },
                                                maintainAspectRatio: false
                                            }} />
                                        </div>
                                        <div className="text-xs text-500 mt-2">
                                            <div className="flex flex-column gap-1">
                                                <span className="inline-flex align-items-center gap-1">
                                                    <span className="w-1rem h-1rem bg-purple-500 border-round"></span>
                                                    Transfers 99.69%
                                                </span>
                                                <span className="inline-flex align-items-center gap-1">
                                                    <span className="w-1rem h-1rem bg-primary border-round"></span>
                                                    Global Accounts 0.31%
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Transactions */}
            <div className="col-12">
                <div className="card">
                    <div className="flex justify-content-between align-items-center mb-4">
                        <h5>Recent transactions</h5>
                        <Button label="View all" className="p-button-text p-button-sm" />
                    </div>

                    <DataTable value={recentTransactions} scrollable>
                        <Column field="date" header="DATE" style={{ width: '10%' }} />
                        <Column field="product" header="PRODUCT" body={productBodyTemplate} style={{ width: '15%' }} />
                        <Column field="details" header="DETAILS" style={{ width: '50%' }} />
                        <Column field="balanceType" header="BALANCE TYPE" style={{ width: '15%' }} />
                        <Column field="amount" header="AMOUNT" body={(rowData) => formatCurrency(rowData.amount)} style={{ width: '10%' }} />
                    </DataTable>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
