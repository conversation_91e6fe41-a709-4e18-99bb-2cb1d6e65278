'use client';
import React from 'react';
import { Button } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Card } from 'primereact/card';

const PaymentsPage = () => {
    const paymentMethods = [
        { name: 'PayPal', logo: '💙', bgColor: 'bg-blue-100' },
        { name: 'Apple Pay', logo: '🍎', bgColor: 'bg-gray-100' },
        { name: 'Stripe', logo: '💚', bgColor: 'bg-green-100' },
        { name: 'Google Pay', logo: '🔴', bgColor: 'bg-red-100' },
        { name: 'Accessibility', logo: '♿', bgColor: 'bg-blue-200' },
        { name: 'Afterpay', logo: '🟢', bgColor: 'bg-green-200' },
        { name: 'UnionPay', logo: '🔵', bgColor: 'bg-blue-300' },
        { name: 'Mastercard', logo: '🔴', bgColor: 'bg-red-200' },
        { name: 'American Express', logo: '🔷', bgColor: 'bg-blue-400' },
        { name: 'PayU', logo: '🟡', bgColor: 'bg-yellow-200' },
        { name: 'Visa', logo: '🔵', bgColor: 'bg-blue-500' },
        { name: 'SEPA', logo: '🟦', bgColor: 'bg-blue-600' },
        { name: 'iDEAL', logo: '🟣', bgColor: 'bg-purple-200' },
        { name: 'Klarna', logo: '🟤', bgColor: 'bg-pink-200' }
    ];

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            {/* <div className="flex align-items-center justify-content-end p-4">
                <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div>
            </div> */}

            {/* Main Content */}
            <div className="max-w-6xl mx-auto px-4 py-6">
                {/* Hero Section */}
                <div className="text-center mb-8">
                    <h1 className="text-2xl font-bold text-900 mb-3">
                        Accept payments from your customers globally
                    </h1>
                    <p className="text-lg text-600 mb-6">
                        Let customers pay seamlessly, eliminate unnecessary fees, and safeguard against fraud.
                    </p>
                    <Button
                        label="Get started"
                        className="bg-blue-500 border-none px-6 py-3 text-lg"
                    />
                </div>

                {/* Payment Methods Section */}
                <div className="grid mb-8 card">
                    <div className="col-12">
                        <div className="pr-4">
                            <h2 className="text-2xl font-bold text-900 mb-3">
                                Accept card and local payment methods in 180+ countries
                            </h2>
                            <p className="text-600 mb-4">
                                Boost checkout rates by pricing in local currency and letting customers pay with their preferred payment methods.
                            </p>
                            <Button
                                label="See supported payment methods"
                                icon="pi pi-external-link"
                                className="p-button-text text-primary p-0"
                            />
                        </div>
                    </div>
                    {/* <div className="col-12 md:col-6">
                        <div className="grid gap-3">
                            {paymentMethods.map((method, index) => (
                                <div key={index} className="col-3">
                                    <div className={`${method.bgColor} p-3 border-round text-center`}>
                                        <div className="text-2xl mb-1">{method.logo}</div>
                                        <div className="text-xs font-medium">{method.name}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div> */}
                </div>

                {/* Features Section */}
                <div className="grid">
                    {/* Eliminate forced FX conversion */}
                    <div className="col-12 md:col-4 mb-6">
                        <div className="pr-4">
                            <h3 className="text-xl font-bold text-900 mb-3">
                                Eliminate forced FX conversion
                            </h3>
                            <p className="text-600 mb-4">
                                Settle payments in the same currency your customer paid in, with like-for-like settlement.
                            </p>
                            <Button
                                label="Learn more"
                                icon="pi pi-external-link"
                                className="p-button-text text-primary p-0 mb-4"
                            />

                            {/* Merchant Account Preview */}
                            {/* <Card className="p-3">
                                <div className="text-sm font-bold mb-3">Merchant Account</div>
                                <div className="text-lg font-bold mb-2">$109,789.37 USD</div>
                                <div className="space-y-2">
                                    <div className="flex align-items-center justify-content-between text-sm">
                                        <div className="flex align-items-center gap-2">
                                            <span className="flag-icon">🇬🇧</span>
                                            <span>GBP</span>
                                        </div>
                                        <span>£2,500.00 GBP</span>
                                    </div>
                                    <div className="flex align-items-center justify-content-between text-sm">
                                        <div className="flex align-items-center gap-2">
                                            <span className="flag-icon">🇪🇺</span>
                                            <span>EUR</span>
                                        </div>
                                        <span>€4,340.25 EUR</span>
                                    </div>
                                    <div className="flex align-items-center justify-content-between text-sm">
                                        <div className="flex align-items-center gap-2">
                                            <span className="flag-icon">🇦🇺</span>
                                            <span>AUD</span>
                                        </div>
                                        <span>$4,200.00 AUD</span>
                                    </div>
                                </div>
                            </Card> */}
                        </div>
                    </div>

                    {/* Flexible integration methods */}
                    <div className="col-12 md:col-4 mb-6">
                        <div className="pr-4">
                            <h3 className="text-xl font-bold text-900 mb-3">
                                Flexible integration methods
                            </h3>
                            <p className="text-600 mb-4">
                                Launch online payments using your preferred integration methods and shopping platform extensions.
                            </p>
                            <Button
                                label="Learn more"
                                icon="pi pi-external-link"
                                className="p-button-text text-primary p-0 mb-4"
                            />

                            {/* Integration Preview */}
                            {/* <div className="bg-gray-900 text-white p-4 border-round text-sm font-mono">
                                <div className="text-green-400 mb-2">// Initialize payment</div>
                                <div className="text-blue-300">const</div>
                                <div className="text-white ml-2">payment = </div>
                                <div className="text-yellow-300">airwallex</div>
                                <div className="ml-4">
                                    <div className="text-blue-300">amount:</div>
                                    <div className="text-orange-300 ml-2">100.00</div>
                                </div>
                                <div className="ml-4">
                                    <div className="text-blue-300">currency:</div>
                                    <div className="text-green-300 ml-2">'USD'</div>
                                </div>
                            </div> */}
                        </div>
                    </div>

                    {/* Seamless checkout experience */}
                    <div className="col-12 md:col-4 mb-6">
                        <div>
                            <h3 className="text-xl font-bold text-900 mb-3">
                                Seamless checkout experience
                            </h3>
                            <p className="text-600 mb-4">
                                Customizable interfaces to match your brand, while boosting conversion and acceptance rates. Explore our demo checkout experience!
                            </p>
                            <Button
                                label="Explore demo"
                                icon="pi pi-external-link"
                                className="p-button-text text-primary p-0 mb-4"
                            />

                            {/* Checkout Preview */}
                            {/* <div className="border-2 border-gray-200 border-round p-3">
                                <div className="bg-gray-100 h-2rem mb-3 border-round"></div>
                                <div className="space-y-2">
                                    <div className="bg-gray-200 h-1rem border-round" style={{ width: '80%' }}></div>
                                    <div className="bg-gray-200 h-1rem border-round" style={{ width: '60%' }}></div>
                                    <div className="bg-gray-200 h-1rem border-round" style={{ width: '70%' }}></div>
                                </div>
                                <div className="bg-green-500 h-2rem mt-3 border-round flex align-items-center justify-content-center">
                                    <span className="text-white text-sm font-bold">Complete Payment</span>
                                </div>
                            </div> */}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PaymentsPage;
