'use client';
import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Badge } from 'primereact/badge';
import { Calendar } from 'primereact/calendar';

interface StatementData {
    id: string;
    statementPeriod: string;
    dueDate: string;
    status: string;
    amountDue: string;
}

const StatementsPage = () => {
    const [dateFrom, setDateFrom] = useState<Date | null>(null);
    const [dateTo, setDateTo] = useState<Date | null>(null);

    const statements: StatementData[] = [
        {
            id: '1',
            statementPeriod: '2025-04-01 to 2025-04-30',
            dueDate: '2025-05-16',
            status: 'Paid',
            amountDue: '0.00 USD'
        },
        {
            id: '2',
            statementPeriod: '2025-03-01 to 2025-03-31',
            dueDate: '2025-04-16',
            status: 'Paid',
            amountDue: '0.00 USD'
        },
        {
            id: '3',
            statementPeriod: '2025-02-01 to 2025-02-28',
            dueDate: '2025-03-16',
            status: 'Paid',
            amountDue: '0.00 USD'
        },
        {
            id: '4',
            statementPeriod: '2025-01-01 to 2025-01-31',
            dueDate: '2025-02-16',
            status: 'Paid',
            amountDue: '0.00 USD'
        }
    ];

    const renderDownloadButton = () => (
        <Button 
            icon="pi pi-download" 
            className="p-button-text p-button-plain text-primary"
            tooltip="Download statement"
        />
    );

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="flex align-items-center justify-content-between px-4 surface-border">
                <div>
                    <h1 className="text-2xl font-bold text-900 m-0">Statements</h1>
                    <div className="w-3rem h-0-25rem bg-primary mt-2"></div>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            <div className="p-4">
                <div className="grid">
                    {/* Latest Statements Card */}
                    <div className="col-12 lg:col-6">
                        <Card className="h-full">
                            <div className="flex align-items-center justify-content-between mb-4">
                                <h2 className="text-xl font-bold text-900 m-0">Latest Statements</h2>
                                <Button 
                                    icon="pi pi-download" 
                                    className="p-button-text p-button-plain text-primary"
                                    tooltip="Download latest statement"
                                />
                            </div>
                            
                            <div className="grid">
                                <div className="col-6">
                                    <div className="mb-4">
                                        <div className="text-sm text-500 mb-1">Amount due</div>
                                        <div className="text-2xl font-bold text-900">0.00 USD</div>
                                    </div>
                                </div>
                                <div className="col-6">
                                    <div className="mb-4">
                                        <div className="text-sm text-500 mb-1">Statement period</div>
                                        <div className="text-sm text-900">2025-04-01 to 2025-04-30</div>
                                    </div>
                                    <div>
                                        <div className="text-sm text-500 mb-1">Due date</div>
                                        <div className="text-sm text-900">2025-05-16</div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>

                    {/* Statement Cycle Card */}
                    <div className="col-12 lg:col-6">
                        <Card className="h-full">
                            <h2 className="text-xl font-bold text-900 mb-4">Statement cycle</h2>
                            
                            <div className="grid">
                                <div className="col-6">
                                    <div className="mb-4">
                                        <div className="text-sm text-500 mb-1">Statement frequency</div>
                                        <div className="text-sm text-900">Monthly</div>
                                    </div>
                                </div>
                                <div className="col-6">
                                    <div className="mb-4">
                                        <div className="text-sm text-500 mb-1">Due date</div>
                                        <div className="text-sm text-900">16th day of every month</div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>

                {/* Statements History */}
                <Card className="mt-4">
                    <div className="flex align-items-center justify-content-between mb-4">
                        <h2 className="text-xl font-bold text-900 m-0">Statements history</h2>
                    </div>

                    {/* Date Filters */}
                    <div className="flex align-items-center gap-3 mb-4">
                        <div className="flex align-items-center gap-2">
                            <i className="pi pi-calendar text-500"></i>
                            <span className="text-sm text-500">Period from</span>
                            <Calendar 
                                value={dateFrom}
                                onChange={(e) => setDateFrom(e.value)}
                                placeholder="Select date"
                                className="w-10rem"
                                showIcon
                            />
                        </div>
                        <div className="flex align-items-center gap-2">
                            <span className="text-sm text-500">Period to</span>
                            <Calendar 
                                value={dateTo}
                                onChange={(e) => setDateTo(e.value)}
                                placeholder="Select date"
                                className="w-10rem"
                                showIcon
                            />
                        </div>
                    </div>

                    {/* Statements Table */}
                    <DataTable 
                        value={statements} 
                        className="p-datatable-sm"
                        responsiveLayout="scroll"
                    >
                        <Column 
                            field="statementPeriod" 
                            header="Statement Period"
                            body={(rowData) => (
                                <span className="text-sm">{rowData.statementPeriod}</span>
                            )}
                        />
                        <Column 
                            field="dueDate" 
                            header="Due Date"
                            body={(rowData) => (
                                <span className="text-sm">{rowData.dueDate}</span>
                            )}
                        />
                        <Column 
                            field="status" 
                            header="Status"
                            body={(rowData) => (
                                <Badge 
                                    value={rowData.status} 
                                    severity="success"
                                    className="text-xs"
                                />
                            )}
                        />
                        <Column 
                            field="amountDue" 
                            header="Amount Due"
                            body={(rowData) => (
                                <span className="text-sm font-medium">{rowData.amountDue}</span>
                            )}
                        />
                        <Column 
                            body={renderDownloadButton}
                            headerStyle={{ width: '4rem' }}
                        />
                    </DataTable>
                </Card>
            </div>
        </div>
    );
};

export default StatementsPage;
