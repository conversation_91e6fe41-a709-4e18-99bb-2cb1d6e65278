'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { TabView, TabPanel } from 'primereact/tabview';
import { ClientUserService } from '@/lib/database/client-users';
import { Database } from '@/types/database.types';

type Profile = Database['public']['Tables']['profiles']['Row'];

const UserManagementPage = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [searchValue, setSearchValue] = useState('');
    const [selectedRole, setSelectedRole] = useState<string | null>(null);
    const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
    const [users, setUsers] = useState<Profile[]>([]);
    const [userStats, setUserStats] = useState({
        total: 0,
        active: 0,
        suspended: 0,
        invited: 0,
        expired: 0
    });
    const [loading, setLoading] = useState(true);

    const userService = useMemo(() => new ClientUserService(), []);

    const roleOptions = [
        { label: 'All Roles', value: null },
        { label: 'Owner', value: 'owner' },
        { label: 'Admin', value: 'admin' },
        { label: 'Employee', value: 'employee' },
        { label: 'Finance admin', value: 'finance_admin' },
        { label: 'Finance manager', value: 'finance_manager' },
        { label: 'Bookkeeper', value: 'bookkeeper' },
        { label: 'Developer', value: 'developer' }
    ];

    const statusOptions = [
        { label: 'All Status', value: null },
        { label: 'Active', value: 'active' },
        { label: 'Suspended', value: 'suspended' },
        { label: 'Invited', value: 'invited' },
        { label: 'Expired', value: 'expired' }
    ];

    // Load data on component mount
    useEffect(() => {
        const loadData = async () => {
            try {
                setLoading(true);
                const [usersData, statsData] = await Promise.all([
                    userService.getUsers(),
                    userService.getUserStats()
                ]);
                setUsers(usersData);
                setUserStats(statsData);
            } catch (error) {
                console.error('Failed to load user data:', error);
            } finally {
                setLoading(false);
            }
        };

        loadData();
    }, [userService]);

    const roles = [
        { name: 'Employee', type: 'Default', users: 0 },
        { name: 'Owner', type: 'Default', users: 1 },
        { name: 'Admin', type: 'Default', users: 1 },
        { name: 'Finance admin', type: 'Default', users: 0 },
        { name: 'Finance manager', type: 'Default', users: 0 },
        { name: 'Bookkeeper', type: 'Default', users: 0 },
        { name: 'Developer', type: 'Default', users: 0 }
    ];

    const renderUserColumn = (rowData: Profile) => (
        <div>
            <div className="font-bold text-900">{rowData.full_name || 'N/A'}</div>
            <div className="text-sm text-500">{rowData.email}</div>
        </div>
    );

    const renderRoleColumn = (rowData: Profile) => (
        <div>
            <div className="font-bold text-900">{rowData.role}</div>
            <div className="text-sm text-500">{rowData.employment_entity || 'N/A'}</div>
        </div>
    );

    const renderManagerColumn = (rowData: Profile) => {
        // For now, we'll show manager_id since we don't have manager details joined
        return (
            <div>
                {rowData.manager_id ? (
                    <span className="text-900">{rowData.manager_id}</span>
                ) : (
                    <span className="text-500">-</span>
                )}
            </div>
        );
    };

    const renderStatusColumn = (rowData: Profile) => (
        <Badge
            value={rowData.status}
            severity={rowData.status === 'active' ? 'success' : 'warning'}
            className="text-xs"
        />
    );

    const renderActionsColumn = () => (
        <Button
            icon="pi pi-ellipsis-h"
            className="p-button-text p-button-plain text-500"
        />
    );

    const renderRoleActionsColumn = (rowData: any) => (
        rowData.users === 0 ? (
            <Button
                icon="pi pi-ellipsis-h"
                className="p-button-text p-button-plain text-500"
            />
        ) : null
    );

    return (
        <div className="min-h-screen bg-gray-50">
            <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                <TabPanel header="Summary">
                    <>
                        {/* Summary Tab */}
                        <div className="flex align-items-center justify-content-between mb-4">
                            <h1 className="text-2xl font-bold text-900 m-0">User Management</h1>
                            <Button label="Invite user" icon="pi pi-plus" className="bg-primary border-primary" />
                        </div>

                        {/* Stats Cards */}
                        <div className="grid mb-4">
                            <div className="col-12 md:col-6">
                                <Card>
                                    <h3 className="text-lg font-bold text-900 mb-3">Invitation status</h3>
                                    <div className="flex justify-content-between align-items-center mb-2">
                                        <span className="text-600">Invited</span>
                                        <span className="text-500">{loading ? '...' : userStats.invited}</span>
                                    </div>
                                    <div className="flex justify-content-between align-items-center">
                                        <span className="text-600">Invitation expired</span>
                                        <span className="text-500">{loading ? '...' : userStats.expired}</span>
                                    </div>
                                </Card>
                            </div>
                            <div className="col-12 md:col-6">
                                <Card>
                                    <h3 className="text-lg font-bold text-900 mb-3">User status</h3>
                                    <div className="flex justify-content-between align-items-center mb-2">
                                        <span className="text-600">Active</span>
                                        <span className="font-bold text-900">{loading ? '...' : userStats.active}</span>
                                    </div>
                                    <div className="flex justify-content-between align-items-center">
                                        <span className="text-600">Suspended</span>
                                        <span className="text-500">{loading ? '...' : userStats.suspended}</span>
                                    </div>
                                </Card>
                            </div>
                        </div>

                        {/* Search and Filters */}
                        <Card className="mb-4">
                            <div className="flex gap-3 align-items-center">
                                <div className="flex-1">
                                    <span className="p-input-icon-left w-full">
                                        <i className="pi pi-search" />
                                        <InputText placeholder="Search name or email" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="w-full" />
                                    </span>
                                </div>
                                <Dropdown value={selectedRole} options={roleOptions} onChange={(e) => setSelectedRole(e.value)} placeholder="Roles" className="w-12rem" />
                                <Dropdown value={selectedStatus} options={statusOptions} onChange={(e) => setSelectedStatus(e.value)} placeholder="Status" className="w-12rem" />
                            </div>
                        </Card>

                        {/* Users Table */}
                        <Card>
                            <DataTable
                                value={users}
                                className="p-datatable-sm"
                                loading={loading}
                                emptyMessage="No users found"
                            >
                                <Column field="full_name" header="User" body={renderUserColumn} />
                                <Column field="role" header="Roles" body={renderRoleColumn} />
                                <Column field="employment_entity" header="Employment Entity" />
                                <Column field="manager_id" header="Manager" body={renderManagerColumn} />
                                <Column field="status" header="Status" body={renderStatusColumn} />
                                <Column body={renderActionsColumn} style={{ width: '4rem' }} />
                            </DataTable>
                        </Card>
                    </>
                </TabPanel>
                <TabPanel header="Roles">
                    <>
                        {/* Roles Tab */}
                        <div className="flex align-items-center justify-content-between mb-4">
                            <h1 className="text-2xl font-bold text-900 m-0">Roles in CONNECTING MATRIX LLC</h1>
                            <Button label="Create custom role" className="bg-primary border-primary" />
                        </div>

                        {/* Roles Table */}
                        <Card>
                            <DataTable value={roles} className="p-datatable-sm">
                                <Column field="name" header="Role" />
                                <Column field="type" header="Role Type" />
                                <Column field="users" header="Users" />
                                <Column body={renderRoleActionsColumn} style={{ width: '4rem' }} />
                            </DataTable>
                        </Card>
                    </>
                </TabPanel>
            </TabView>
        </div>
    );
};

export default UserManagementPage;
