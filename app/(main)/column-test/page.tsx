'use client';

import React, { useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { Message } from 'primereact/message';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Badge } from 'primereact/badge';
import { columnApi } from '@/lib/api/column-api';

const ColumnTestPage = () => {
    const [loading, setLoading] = useState(false);
    const [healthStatus, setHealthStatus] = useState<boolean | null>(null);
    const [environment, setEnvironment] = useState<string>('');
    const [isConfigured, setIsConfigured] = useState<boolean>(false);
    const [entities, setEntities] = useState<any[]>([]);
    const [accounts, setAccounts] = useState<any[]>([]);
    const [transfers, setTransfers] = useState<any[]>([]);
    const [error, setError] = useState<string>('');

    useEffect(() => {
        checkHealth();
    }, []);

    const checkHealth = async () => {
        try {
            const data = await columnApi.checkHealth();

            setIsConfigured(data.configured);
            setHealthStatus(data.healthy);
            setEnvironment(data.environment);

            if (!data.configured) {
                setError('Column API is not configured. Please set COLUMN_API_KEY environment variable.');
            } else if (!data.healthy) {
                setError(data.message || 'Column API is not responding');
            } else {
                setError('');
            }
        } catch (err: any) {
            setIsConfigured(false);
            setHealthStatus(false);
            setError(`Failed to check Column API status: ${err.message}`);
        }
    };

    const loadEntities = async () => {
        setLoading(true);
        setError('');
        try {
            const data = await columnApi.listEntities({ limit: 10 });
            setEntities(data.entities || data.data || []);
        } catch (err: any) {
            setError(`Failed to load entities: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    const loadAccounts = async () => {
        setLoading(true);
        setError('');
        try {
            const data = await columnApi.listBankAccounts({ limit: 10 });
            setAccounts(data.data || []);
        } catch (err: any) {
            setError(`Failed to load accounts: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    const loadTransfers = async () => {
        setLoading(true);
        setError('');
        try {
            const data = await columnApi.listTransfers({ limit: 10 });
            setTransfers(data.data || []);
        } catch (err: any) {
            setError(`Failed to load transfers: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    const createTestEntity = async (type: 'person' | 'business' = 'person') => {
        setLoading(true);
        setError('');
        try {
            const entity = await columnApi.createTestEntity(type);
            setEntities(prev => [entity, ...prev]);
            setError('');
        } catch (err: any) {
            setError(`Failed to create entity: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    const statusBodyTemplate = (rowData: any) => {
        const severity = rowData.status === 'active' || rowData.status === 'approved' ? 'success' : 
                        rowData.status === 'pending' ? 'warning' : 'danger';
        return <Badge value={rowData.status} severity={severity} />;
    };

    const amountBodyTemplate = (rowData: any) => {
        return `$${(rowData.amount / 100).toFixed(2)}`;
    };

    const dateBodyTemplate = (rowData: any) => {
        return new Date(rowData.created_at).toLocaleDateString();
    };

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <h1>Column Banking Integration Test</h1>
                    <p className="text-600 line-height-3 m-0">
                        Test your Column API integration and verify connectivity.
                    </p>
                </div>
            </div>

            {/* Health Status */}
            <div className="col-12 md:col-6">
                <Card title="API Health Status" className="h-full">
                    <div className="flex align-items-center gap-3 mb-3">
                        <i className={`pi ${!isConfigured ? 'pi-exclamation-triangle text-orange-500' :
                                      healthStatus === true ? 'pi-check-circle text-green-500' :
                                      healthStatus === false ? 'pi-times-circle text-red-500' :
                                      'pi-spin pi-spinner'} text-2xl`}></i>
                        <span className="text-xl">
                            {!isConfigured ? 'Not Configured' :
                             healthStatus === true ? 'Connected' :
                             healthStatus === false ? 'Disconnected' :
                             'Checking...'}
                        </span>
                    </div>

                    <div className="mb-3">
                        <strong>Configuration:</strong>
                        <Badge
                            value={isConfigured ? 'Configured' : 'Missing API Key'}
                            severity={isConfigured ? 'success' : 'danger'}
                            className="ml-2"
                        />
                    </div>

                    <div className="mb-3">
                        <strong>Environment:</strong>
                        <Badge
                            value={environment || 'Not Set'}
                            severity={environment === 'sandbox' ? 'warning' : environment === 'production' ? 'success' : 'info'}
                            className="ml-2"
                        />
                    </div>

                    <Button
                        label="Refresh Status"
                        icon="pi pi-refresh"
                        onClick={checkHealth}
                        // disabled={!isConfigured}
                        className="p-button-outlined"
                    />
                </Card>
            </div>

            {/* Quick Actions or Setup Instructions */}
            <div className="col-12 md:col-6">
                {isConfigured ? (
                    <Card title="Quick Actions" className="h-full">
                        <div className="flex flex-column gap-2">
                            <Button
                                label="Load Entities"
                                icon="pi pi-users"
                                onClick={loadEntities}
                                loading={loading}
                                className="p-button-outlined"
                            />
                            <Button
                                label="Load Bank Accounts"
                                icon="pi pi-wallet"
                                onClick={loadAccounts}
                                loading={loading}
                                className="p-button-outlined"
                            />
                            <Button
                                label="Load Transfers"
                                icon="pi pi-arrow-right-arrow-left"
                                onClick={loadTransfers}
                                loading={loading}
                                className="p-button-outlined"
                            />
                            {environment === 'sandbox' && (
                                <>
                                    <Button
                                        label="Create Test Person"
                                        icon="pi pi-plus"
                                        onClick={() => createTestEntity('person')}
                                        loading={loading}
                                        className="p-button-success"
                                    />
                                    <Button
                                        label="Create Test Business"
                                        icon="pi pi-building"
                                        onClick={() => createTestEntity('business')}
                                        loading={loading}
                                        className="p-button-success"
                                    />
                                </>
                            )}
                        </div>
                    </Card>
                ) : (
                    <Card title="Setup Required" className="h-full">
                        <div className="mb-3">
                            <p className="text-600 line-height-3">
                                To test Column banking features, you need to configure your API key.
                            </p>
                        </div>

                        <div className="mb-3">
                            <h6>Setup Steps:</h6>
                            <ol className="text-sm line-height-3 pl-3">
                                <li>Sign up at <a href="https://dashboard.column.com/register" target="_blank" rel="noopener noreferrer" className="text-primary">Column Dashboard</a></li>
                                <li>Get your sandbox API key</li>
                                <li>Add to your <code>.env.local</code> file:</li>
                            </ol>
                        </div>

                        <div className="mb-3">
                            <pre className="bg-gray-100 p-2 border-round text-sm">
{`COLUMN_API_KEY=test_xxxxxxxxxxxxxxxx
COLUMN_ENVIRONMENT=sandbox`}
                            </pre>
                        </div>

                        <div className="text-sm text-600">
                            <p>See <strong>COLUMN_SETUP.md</strong> for detailed instructions.</p>
                        </div>
                    </Card>
                )}
            </div>

            {/* Error Display */}
            {error && (
                <div className="col-12">
                    <Message severity="error" text={error} className="w-full" />
                </div>
            )}

            {/* Entities Table */}
            {entities.length > 0 && (
                <div className="col-12">
                    <Card title="Entities">
                        <DataTable value={entities}>
                            <Column field="id" header="ID" style={{ width: '200px' }} />
                            <Column field="type" header="Type" />
                            <Column
                                field="first_name"
                                header="Name"
                                body={(rowData) =>
                                    rowData.type === 'person'
                                        ? `${rowData.first_name} ${rowData.last_name}`
                                        : rowData.legal_name
                                }
                            />
                            <Column field="email" header="Email" />
                            <Column field="status" header="Status" body={statusBodyTemplate} />
                            <Column field="created_at" header="Created" body={dateBodyTemplate} />
                        </DataTable>
                    </Card>
                </div>
            )}

            {/* Bank Accounts Table */}
            {accounts.length > 0 && (
                <div className="col-12">
                    <Card title="Bank Accounts">
                        <DataTable value={accounts}>
                            <Column field="id" header="ID" style={{ width: '200px' }} />
                            <Column field="account_type" header="Type" />
                            <Column field="nickname" header="Nickname" />
                            <Column field="balance" header="Balance" body={amountBodyTemplate} />
                            <Column field="status" header="Status" body={statusBodyTemplate} />
                            <Column field="created_at" header="Created" body={dateBodyTemplate} />
                        </DataTable>
                    </Card>
                </div>
            )}

            {/* Transfers Table */}
            {transfers.length > 0 && (
                <div className="col-12">
                    <Card title="Transfers">
                        <DataTable value={transfers}>
                            <Column field="id" header="ID" style={{ width: '200px' }} />
                            <Column field="type" header="Type" />
                            <Column field="amount" header="Amount" body={amountBodyTemplate} />
                            <Column field="description" header="Description" />
                            <Column field="status" header="Status" body={statusBodyTemplate} />
                            <Column field="created_at" header="Created" body={dateBodyTemplate} />
                        </DataTable>
                    </Card>
                </div>
            )}
        </div>
    );
};

export default ColumnTestPage;
