'use client';
import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Avatar } from 'primereact/avatar';
import { InputSwitch } from 'primereact/inputswitch';
import { Button } from 'primereact/button';

const SecurityPage = () => {
    const [loggingInEnabled, setLoggingInEnabled] = useState(true);
    const [creatingTransferEnabled, setCreatingTransferEnabled] = useState(false);

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="flex align-items-center justify-content-between px-4 surface-border">
                <div>
                    <h1 className="text-2xl font-bold text-900 m-0">Security</h1>
                    <div className="w-3rem h-0-25rem bg-orange-500 mt-2"></div>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            <div className="p-4">
                {/* Account-wide two-factor authentication (2FA) */}
                <Card className="mb-4">
                    <div className="mb-4">
                        <h2 className="text-xl font-bold text-900 mb-2">Account-wide two-factor authentication (2FA)</h2>
                        <p className="text-600 mb-3">
                            Two-factor authentication (2FA) provides an extra layer of security to protect the safety of your account.{' '}
                            <Button 
                                label="Learn more" 
                                className="p-button-link p-0 text-primary"
                                style={{ textDecoration: 'underline' }}
                            />
                        </p>
                    </div>

                    <div className="mb-4">
                        <p className="text-900 mb-4">
                            Require <strong>each user</strong> to set up and use 2FA for:
                        </p>

                        {/* Logging in */}
                        <div className="flex align-items-center justify-content-between mb-4 pb-3 border-bottom-1 surface-border">
                            <div className="flex align-items-center gap-3">
                                <i className="pi pi-user text-xl text-500"></i>
                                <span className="text-900">Logging in</span>
                            </div>
                            <InputSwitch 
                                checked={loggingInEnabled}
                                onChange={(e) => setLoggingInEnabled(e.value)}
                            />
                        </div>

                        {/* Creating a transfer */}
                        <div className="flex align-items-center justify-content-between">
                            <div className="flex align-items-center gap-3">
                                <i className="pi pi-send text-xl text-500"></i>
                                <span className="text-900">Creating a transfer</span>
                            </div>
                            <InputSwitch 
                                checked={creatingTransferEnabled}
                                onChange={(e) => setCreatingTransferEnabled(e.value)}
                            />
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default SecurityPage;
