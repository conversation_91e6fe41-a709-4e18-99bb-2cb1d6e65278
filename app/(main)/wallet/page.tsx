'use client';
import React, { useState } from 'react';
import { Tab<PERSON>iew, TabPanel } from 'primereact/tabview';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Card } from 'primereact/card';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';
import { Message } from 'primereact/message';

interface Currency {
    code: string;
    name: string;
    flag: string;
    balance: string;
    globalAccounts: number;
}

interface Transaction {
    date: string;
    product: string;
    details: string;
    balanceType: string;
    amount: string;
    type: 'transfer' | 'global-account';
}

const WalletPage = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [selectedCurrency, setSelectedCurrency] = useState('USD');
    const [searchValue, setSearchValue] = useState('');

    const currencies: Currency[] = [
        { code: 'USD', name: 'US Dollar', flag: '🇺🇸', balance: '0.00 USD', globalAccounts: 3 },
        { code: 'AUD', name: 'Australian Dollar', flag: '🇦🇺', balance: '0.00 AUD', globalAccounts: 2 },
        { code: 'CAD', name: 'Canadian Dollar', flag: '🇨🇦', balance: '0.00 CAD', globalAccounts: 2 },
        { code: 'EUR', name: 'Euro', flag: '🇪🇺', balance: '0.00 EUR', globalAccounts: 2 },
        { code: 'GBP', name: 'Great Britain Pound', flag: '🇬🇧', balance: '0.00 GBP', globalAccounts: 2 }
    ];

    const transactions: Transaction[] = [
        {
            date: '2025-06-05',
            product: 'Transfers',
            details: 'Pay MUHAMMAD MEMOON 43,024.44 PKR (CPS Payment)',
            balanceType: 'Cash',
            amount: '-154.53 USD',
            type: 'transfer'
        },
        {
            date: '2025-06-04',
            product: 'Transfers',
            details: 'Pay Komal Shahid 72,500.00 PKR (Salary)',
            balanceType: 'Cash',
            amount: '-260.40 USD',
            type: 'transfer'
        },
        {
            date: '2025-06-04',
            product: 'Transfers',
            details: 'Pay MUHAMMAD MEMOON 40,000.00 PKR (Salary)',
            balanceType: 'Cash',
            amount: '-143.67 USD',
            type: 'transfer'
        },
        {
            date: '2025-06-04',
            product: 'Global Accounts',
            details: 'Fee for deposit from WISE US INC (MUHAMMAD A From Muhammad Abeer Via WISE)',
            balanceType: 'Cash',
            amount: '-2.40 USD',
            type: 'global-account'
        },
        {
            date: '2025-06-04',
            product: 'Global Accounts',
            details: 'Deposit from WISE US INC (MUHAMMAD A From Muhammad Abeer Via WISE) | CONNECTING MATRIX LLC **********',
            balanceType: 'Cash',
            amount: '+798.87 USD',
            type: 'global-account'
        }
    ];

    const currencyOptions = [
        { label: 'USD', value: 'USD' },
        { label: 'AUD', value: 'AUD' },
        { label: 'CAD', value: 'CAD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'GBP', value: 'GBP' }
    ];

    const renderBalancesTab = () => (
        <div className="p-4">
            {/* Header Section */}
            <div className="flex justify-content-between align-items-center mb-4">
                <div className="flex align-items-center gap-3">
                    <span className="text-2xl">🇺🇸</span>
                    <div>
                        <h2 className="text-3xl font-bold m-0">0.00</h2>
                        <p className="text-500 m-0">Total balance</p>
                    </div>
                    <Dropdown 
                        value={selectedCurrency} 
                        options={currencyOptions}
                        onChange={(e) => setSelectedCurrency(e.value)}
                        className="ml-2"
                    />
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            {/* Cash Balances Section */}
            <Card className="mb-4">
                <div className="flex justify-content-between align-items-center mb-4">
                    <div>
                        <h3 className="text-xl font-bold m-0 mb-2">Cash balances</h3>
                        <span className="text-sm text-500">0.00 USD</span>
                    </div>
                    <div className="flex gap-2">
                        <Button label="Discover equatePay" className="p-button-text" />
                        <Button label="Add funds" className="p-button-outlined" />
                        <Button label="Convert" className="p-button-outlined" />
                        <Button label="New transfer" icon="pi pi-chevron-down" iconPos="right" />
                    </div>
                </div>

                {/* Currency Table */}
                <DataTable value={currencies} className="p-datatable-sm">
                    <Column 
                        field="code" 
                        header="Currency" 
                        body={(rowData) => (
                            <div className="flex align-items-center gap-2">
                                <span className="text-lg">{rowData.flag}</span>
                                <div>
                                    <div className="font-bold">{rowData.code}</div>
                                    <div className="text-sm text-500">{rowData.name}</div>
                                </div>
                            </div>
                        )}
                    />
                    <Column 
                        field="globalAccounts" 
                        header="Global Accounts" 
                        body={(rowData) => (
                            <span className="text-primary">{rowData.globalAccounts} active</span>
                        )}
                    />
                    <Column field="scheduledIn" header="Scheduled In" />
                    <Column field="scheduledOut" header="Scheduled Out" />
                    <Column 
                        field="balance" 
                        header="Available Balance" 
                        body={(rowData) => (
                            <div className="flex justify-content-between align-items-center">
                                <span>{rowData.balance}</span>
                                <Button icon="pi pi-ellipsis-h" className="p-button-text p-button-plain" />
                            </div>
                        )}
                    />
                </DataTable>

                <div className="text-center mt-4">
                    <Button label="Edit currency display" icon="pi pi-pencil" className="p-button-text" />
                </div>
            </Card>
        </div>
    );

    const renderTransactionsTab = () => (
        <div className="p-4">
            {/* Header Section */}
            <div className="flex justify-content-between align-items-center mb-4">
                <div className="flex align-items-center gap-3">
                    <h2 className="text-2xl font-bold m-0">Transactions</h2>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            {/* Alert Message */}
            <Message
                severity="warn"
                text="You have no scheduled transactions in the next 30 days."
                className="mb-4"
            />

            {/* Search and Filters */}
            <Card className="mb-4">
                <div className="flex flex-column gap-3">
                    <div className="flex align-items-center gap-2">
                        <i className="pi pi-search text-500"></i>
                        <InputText
                            placeholder="Search by transaction ID or details"
                            value={searchValue}
                            onChange={(e) => setSearchValue(e.target.value)}
                            className="flex-1"
                        />
                        <Button icon="pi pi-download" className="p-button-outlined" />
                    </div>

                    <div className="flex gap-3">
                        <div className="flex align-items-center gap-2">
                            <i className="pi pi-calendar text-500"></i>
                            <span className="text-sm text-500">Settled from</span>
                            <InputText placeholder="To" className="w-8rem" />
                        </div>
                        <Dropdown
                            placeholder="Product"
                            className="w-10rem"
                        />
                        <Dropdown
                            placeholder="Currency"
                            className="w-8rem"
                        />
                        <Dropdown
                            placeholder="Balance type"
                            className="w-10rem"
                        />
                    </div>
                </div>
            </Card>

            {/* Transactions Table */}
            <Card>
                <DataTable value={transactions} className="">
                    <Column field="date" header="Date" />
                    <Column
                        field="product"
                        header="Product"
                        body={(rowData) => (
                            <div className="flex align-items-center gap-2">
                                <i className={`pi ${rowData.type === 'transfer' ? 'pi-send' : 'pi-globe'} text-500`}></i>
                                <span>{rowData.product}</span>
                            </div>
                        )}
                    />
                    <Column
                        field="details"
                        header="Details"
                        style={{ maxWidth: '300px' }}
                        body={(rowData) => (
                            <span className="text-sm">{rowData.details}</span>
                        )}
                    />
                    <Column field="balanceType" header="BALANCE TYPE" />
                    <Column
                        field="amount"
                        header="Amount"
                        body={(rowData) => (
                            <span className={rowData.amount.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                                {rowData.amount}
                            </span>
                        )}
                    />
                </DataTable>
            </Card>
        </div>
    );

    const renderLinkedAccountsTab = () => (
        <div className="p-4">
            {/* Header Section */}
            <div className="flex justify-content-between align-items-center mb-6">
                <div className="flex align-items-center gap-3">
                    <h2 className="text-2xl font-bold m-0">Linked Accounts</h2>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            {/* Empty State */}
            <div className="text-center py-8">
                <div className="mb-6">
                    <div className="inline-flex align-items-center justify-content-center w-12rem h-8rem mb-4">
                        <svg width="120" height="80" viewBox="0 0 120 80" fill="none">
                            <circle cx="20" cy="20" r="3" fill="#ccc"/>
                            <circle cx="20" cy="30" r="3" fill="#ccc"/>
                            <circle cx="20" cy="40" r="3" fill="#ccc"/>
                            <circle cx="20" cy="50" r="3" fill="#ccc"/>
                            <path d="M40 10 L100 10 L100 70 L40 70 Z" fill="#e0e0e0"/>
                            <circle cx="85" cy="25" r="8" fill="#ff6b35"/>
                            <path d="M80 25 L90 25 M85 20 L85 30" stroke="white" strokeWidth="2"/>
                        </svg>
                    </div>
                </div>

                <h3 className="text-2xl font-bold mb-3">Link your external bank account</h3>
                <p className="text-500 mb-2 max-w-30rem mx-auto">
                    Link your external bank account now to easily add funds to equate.
                </p>
                <p className="text-500 mb-2 max-w-30rem mx-auto">
                    All your linked accounts will be listed here.
                </p>
                <p className="text-500 mb-6 max-w-30rem mx-auto">
                    Enabling direct debit allows for free and instant transfers without needing to leave equate.
                </p>

                <Button
                    label="Link external bank account"
                    className="px-6"
                    style={{ backgroundColor: '#6366f1', border: 'none' }}
                />
            </div>
        </div>
    );

    const renderSettingsTab = () => (
        <div className="p-4">
            {/* Header Section */}
            <div className="flex justify-content-between align-items-center mb-6">
                <div className="flex align-items-center gap-3">
                    <h2 className="text-2xl font-bold m-0">Settings</h2>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            {/* Notification Settings */}
            <div className="mb-6">
                <h3 className="text-xl font-bold mb-2">Notification settings</h3>
                <p className="text-500 mb-4">
                    Notification settings will apply to all users on this account. You can manage your personal notifications in your{' '}
                    <a href="#" className="text-primary">User profile</a>.
                </p>

                <Card>
                    <div className="flex justify-content-between align-items-center">
                        <div>
                            <h5 className="font-bold mb-2 text-xl">Low balance alerts</h5>
                            <p className="text-500 m-0">Get alerted when the available balance falls below a set amount.</p>
                        </div>
                        <Button
                            label="Set up"
                            className="p-button-outlined"
                        />
                    </div>
                </Card>
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50">
            <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                <TabPanel header="Balances">
                    {renderBalancesTab()}
                </TabPanel>
                <TabPanel header="Transactions">
                    {renderTransactionsTab()}
                </TabPanel>
                <TabPanel header="Linked Accounts">
                    {renderLinkedAccountsTab()}
                </TabPanel>
                <TabPanel header="Settings">
                    {renderSettingsTab()}
                </TabPanel>
            </TabView>
        </div>
    );
};

export default WalletPage;
