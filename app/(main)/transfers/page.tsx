'use client';
import React, { useState } from 'react';
import { Tab<PERSON>iew, TabPanel } from 'primereact/tabview';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Card } from 'primereact/card';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';
import { Checkbox } from 'primereact/checkbox';
import { Calendar } from 'primereact/calendar';
import { RadioButton } from 'primereact/radiobutton';

interface Transfer {
    id: string;
    date: string;
    status: string;
    recipient: {
        name: string;
        iban: string;
    };
    reference: string;
    youPay: string;
    recipientGets: string;
}

const TransfersPage = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [selectedTransfers, setSelectedTransfers] = useState<string[]>([]);
    const [searchValue, setSearchValue] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateRange, setDateRange] = useState<Date[]>([]);
    const [refundOption, setRefundOption] = useState('currency-you-pay');
    const [summarySubTab, setSummarySubTab] = useState('transfers');

    const transfers: Transfer[] = [
        {
            id: '1',
            date: '2026-06-06',
            status: 'Paid',
            recipient: {
                name: 'MUHAMMAD MEMOON',
                iban: 'IBAN • ***********************'
            },
            reference: 'OPS Payment',
            youPay: '164.53 USD',
            recipientGets: '43,024.44 PKR'
        },
        {
            id: '2',
            date: '2026-06-04',
            status: 'Paid',
            recipient: {
                name: 'Komal Shahid',
                iban: 'IBAN • ***********************'
            },
            reference: 'Salary',
            youPay: '260.40 USD',
            recipientGets: '72,500.00 PKR'
        },
        {
            id: '3',
            date: '2026-06-04',
            status: 'Paid',
            recipient: {
                name: 'MUHAMMAD MEMOON',
                iban: 'IBAN • ***********************'
            },
            reference: 'Salary',
            youPay: '143.67 USD',
            recipientGets: '40,000.00 PKR'
        },
        {
            id: '4',
            date: '2026-06-04',
            status: 'Paid',
            recipient: {
                name: 'MISBAH TAHIRA',
                iban: 'IBAN • ***********************'
            },
            reference: 'Salary',
            youPay: '126.71 USD',
            recipientGets: '35,000.00 PKR'
        },
        {
            id: '5',
            date: '2026-06-04',
            status: 'Paid',
            recipient: {
                name: 'Zain Ul Abideen',
                iban: 'IBAN • ***********************'
            },
            reference: 'Salary',
            youPay: '513.61 USD',
            recipientGets: '143,000.00 PKR'
        }
    ];

    const statusOptions = [
        { label: 'All Status', value: '' },
        { label: 'Paid', value: 'Paid' },
        { label: 'Pending', value: 'Pending' },
        { label: 'Failed', value: 'Failed' }
    ];

    const renderSummaryTab = () => (
        <div className="p-4">
            {/* Sub-navigation */}
            <div className="flex align-items-center justify-content-between mb-4">
                <div className="flex gap-4">
                    <Button
                        label="Transfers"
                        className={`p-button-text ${summarySubTab === 'transfers' ? 'font-bold border-bottom-2 border-primary' : 'text-500'}`}
                        onClick={() => setSummarySubTab('transfers')}
                    />
                    <Button
                        label="Batch transfers"
                        className={`p-button-text ${summarySubTab === 'batch' ? 'font-bold border-bottom-2 border-primary' : 'text-500'}`}
                        onClick={() => setSummarySubTab('batch')}
                    />
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            {summarySubTab === 'transfers' ? renderTransfersContent() : renderBatchTransfersContent()}
        </div>
    );

    const renderTransfersContent = () => (
        <>
            {/* Action Bar */}
            <div className="flex align-items-center justify-content-between mb-4">
                <Button
                    label="New transfer"
                    icon="pi pi-chevron-down"
                    iconPos="right"
                    className="bg-blue-500 border-none"
                />
            </div>

            {/* Search and Filters */}
            <div className="flex align-items-center gap-3 mb-4">
                <div className="flex align-items-center gap-2 flex-1">
                    {/* <i className="pi pi-search text-500"></i> */}
                    <InputText
                        placeholder="Search recipient, account number, transfer ID, reference, description, or request ID"
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        className="flex-1"
                    />
                </div>
                <Dropdown
                    value={statusFilter}
                    options={statusOptions}
                    onChange={(e) => setStatusFilter(e.value)}
                    placeholder="Status"
                    className="w-8rem"
                />
                <div className="flex align-items-center gap-2">
                    {/* <i className="pi pi-calendar text-500"></i>
                    <span className="text-sm">Creation date</span> */}
                    <Calendar
                        value={dateRange}
                        onChange={(e) => setDateRange(e.value as Date[])}
                        selectionMode="range"
                        placeholder="2026-03-14 - 2026-06-11"
                        className="w-12rem"
                    />
                    <Button icon="pi pi-times" className="p-button-text p-button-plain" />
                </div>
                <Button icon="pi pi-download" className="p-button-outlined" />
            </div>

            {/* Transfers Table */}
            <DataTable
                value={transfers}
                className=""
                selection={selectedTransfers}
                onSelectionChange={(e) => setSelectedTransfers(e.value)}
                dataKey="id"
            >
                <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
                <Column
                    field="date"
                    header="Transfer Date"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.date}</span>
                    )}
                />
                <Column
                    field="status"
                    header="Status"
                    body={(rowData) => (
                        <Badge
                            value={rowData.status}
                            severity={rowData.status === 'Paid' ? 'success' : 'warning'}
                            className="text-xs"
                        />
                    )}
                />
                <Column
                    field="recipient"
                    header="Recipient"
                    body={(rowData) => (
                        <div>
                            <div className="font-bold text-sm">{rowData.recipient.name}</div>
                            <div className="text-xs text-500">{rowData.recipient.iban}</div>
                        </div>
                    )}
                />
                <Column
                    field="reference"
                    header="Reference & Description"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.reference}</span>
                    )}
                />
                <Column
                    field="youPay"
                    header="You Pay"
                    body={(rowData) => (
                        <span className="text-sm font-bold">{rowData.youPay}</span>
                    )}
                />
                <Column
                    field="recipientGets"
                    header="Recipient Gets"
                    body={(rowData) => (
                        <span className="text-sm">{rowData.recipientGets}</span>
                    )}
                />
            </DataTable>

            {/* Pagination */}
            <div className="flex align-items-center justify-content-between mt-4">
                <div className="flex align-items-center gap-2">
                    <Button icon="pi pi-angle-double-left" className="p-button-text" />
                    <Button icon="pi pi-angle-left" className="p-button-text" />
                    <Button label="1" className="p-button-text font-bold" />
                    <Button label="2" className="p-button-text" />
                    <Button label="3" className="p-button-text" />
                    <Button icon="pi pi-angle-right" className="p-button-text" />
                    <Button icon="pi pi-angle-double-right" className="p-button-text" />
                </div>
                <div className="flex align-items-center gap-2">
                    <span className="text-sm">Rows per page:</span>
                    <Dropdown value={10} options={[{label: '10', value: 10}]} className="w-5rem" />
                    <span className="text-sm">1 - 10 of 30</span>
                </div>
            </div>
        </>
    );

    const renderBatchTransfersContent = () => (
        <div className="grid">
            <div className="col-12 lg:col-8">
                {/* Main Content */}
                <div className="mb-6">
                    <h2 className="text-4xl font-bold mb-4">Simplify your workflow with<br />Batch transfers</h2>

                    {/* Features */}
                    <div className="mb-6">
                        <div className="flex align-items-start gap-3 mb-4">
                            <div className="w-3rem h-3rem bg-orange-100 border-circle flex align-items-center justify-content-center">
                                <i className="pi pi-globe text-orange-500 text-xl"></i>
                            </div>
                            <div>
                                <h4 className="font-bold text-lg mb-2">Fast, cost-effective international transfers</h4>
                                <p className="text-500 m-0">
                                    Streamline payments to suppliers and employees around the world and access market-leading FX rates no matter the transaction size.
                                </p>
                            </div>
                        </div>

                        <div className="flex align-items-start gap-3 mb-4">
                            <div className="w-3rem h-3rem bg-yellow-100 border-circle flex align-items-center justify-content-center">
                                <i className="pi pi-code text-yellow-600 text-xl"></i>
                            </div>
                            <div>
                                <h4 className="font-bold text-lg mb-2">No-code payouts to multiple parties in one go</h4>
                                <p className="text-500 m-0">
                                    Streamline multiple payments to global suppliers and employees without ever having to write a single line of code.
                                </p>
                            </div>
                        </div>

                        <div className="flex align-items-start gap-3 mb-6">
                            <div className="w-3rem h-3rem bg-red-100 border-circle flex align-items-center justify-content-center">
                                <i className="pi pi-file text-red-500 text-xl"></i>
                            </div>
                            <div>
                                <h4 className="font-bold text-lg mb-2">Intuitive and dynamic templates to guide you</h4>
                                <p className="text-500 m-0">
                                    Simply download and complete the template that best meets your requirements, upload it to Airwallex, and book your transfer.
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                        <Button
                            label="New batch transfer"
                            className="bg-blue-500 border-none px-4 py-3"
                        />
                        <Button
                            label="Learn more"
                            className="p-button-outlined px-4 py-3"
                        />
                    </div>
                </div>
            </div>

            <div className="col-12 lg:col-4">
                {/* Transfer Details Card */}
                <div className="bg-white border-round p-4 shadow-1">
                    <h4 className="font-bold mb-4">Transfer details</h4>

                    <div className="grid text-sm mb-4">
                        <div className="col-6 text-500">RECIPIENT</div>
                        <div className="col-6 text-500">REFERENCE</div>
                        <div className="col-6 text-500">STATUS</div>
                        <div className="col-6 text-500">AMOUNT</div>
                    </div>

                    {/* Sample entries */}
                    <div className="grid text-sm mb-3 pb-2 border-bottom-1 surface-border">
                        <div className="col-6">Jenny Wilson</div>
                        <div className="col-6">Monthly payroll</div>
                        <div className="col-6"><Badge value="Processing" severity="warning" className="text-xs" /></div>
                        <div className="col-6">1,160.00 USD</div>
                    </div>

                    <div className="grid text-sm mb-3 pb-2 border-bottom-1 surface-border">
                        <div className="col-6">Neil Patel</div>
                        <div className="col-6">Contract salary</div>
                        <div className="col-6"><Badge value="Sent" severity="success" className="text-xs" /></div>
                        <div className="col-6">6,000.00 AUD</div>
                    </div>

                    <div className="grid text-sm mb-3 pb-2 border-bottom-1 surface-border">
                        <div className="col-6">Yuki Anderson</div>
                        <div className="col-6">Monthly payroll</div>
                        <div className="col-6"><Badge value="Sent" severity="success" className="text-xs" /></div>
                        <div className="col-6">4,370.00 HKD</div>
                    </div>

                    <div className="text-center mt-4">
                        <div className="inline-flex align-items-center gap-2 bg-indigo-50 px-3 py-2 border-round">
                            <span className="text-sm font-bold">February Payroll</span>
                            <div className="w-2rem h-2rem bg-indigo-500 border-circle flex align-items-center justify-content-center">
                                <span className="text-white text-xs font-bold">142</span>
                            </div>
                            <span className="text-xs text-500">payments</span>
                        </div>
                        <div className="mt-2">
                            <Badge value="Booked" severity="info" className="text-xs" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderTransferApprovalsTab = () => (
        <div className="p-4">
            {/* Header */}
            <div className="flex justify-content-between align-items-center mb-6">
                <div>
                    <h2 className="text-2xl font-bold m-0 mb-2">Transfer approvals</h2>
                    <p className="text-500 m-0">
                        Existing transfers pending approval can be viewed in{' '}
                        <a href="#" className="text-primary">Transfers</a>
                    </p>
                </div>
                {/* <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div> */}
            </div>

            {/* Approval workflow section */}
            <Card className="mb-4">
                <div className="flex justify-content-between align-items-start">
                    <div className="flex-1">
                        <h3 className="text-xl font-bold mb-3">Approval workflow</h3>
                        <p className="text-500 mb-0" style={{ maxWidth: '500px' }}>
                            Route transfers and batch transfers through approval layers based on transfer amounts.
                            Users or roles with the Create / Edit permission to Transfers can be assigned as
                            approvers in the flow.
                        </p>
                    </div>
                    <Button
                        label="Set up transfer approval workflow"
                        icon="pi pi-plus"
                        className="text-primary p-button-text"
                    />
                </div>
            </Card>

            {/* Workflow change policy section */}
            <Card>
                <div className="flex justify-content-between align-items-start">
                    <div className="flex-1">
                        <h3 className="text-xl font-bold mb-3">Workflow change policy</h3>
                        <div className="mb-4">
                            <p className="text-500 mb-2">
                                Any changes that impact the approval workflow, including activation, edits and
                                deactivation, will require approval.{' '}
                                <a href="#" className="text-primary">Learn more</a>
                            </p>
                        </div>
                        <div className="bg-gray-50 p-3 border-round">
                            <p className="text-500 mb-2">Changes to the workflow will require approval from the following Owner:</p>
                            <p className="font-bold text-900 m-0">Muhammad Abeer</p>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    );

    const renderSettingsTab = () => (
        <div className="p-4">
            {/* Header */}
            {/* <div className="flex justify-content-between align-items-center mb-6">
                <div className="flex align-items-center gap-2">
                    <span className="text-sm"><EMAIL></span>
                    <Avatar icon="pi pi-user" className="bg-gray-300" />
                </div>
            </div> */}

            {/* Transfer refunds section */}
            <div className="mb-6">
                <h2 className="text-2xl font-bold mb-2">Transfer refunds</h2>
                <p className="text-500 mb-4">
                    Choose the type of currency that cancelled transfers will be refunded in.
                </p>

                <div className="grid">
                    <div className="col-12 md:col-6">
                        <Card className="h-full">
                            <div className="flex align-items-start gap-3">
                                <RadioButton
                                    inputId="currency-you-pay"
                                    name="refund"
                                    value="currency-you-pay"
                                    onChange={(e) => setRefundOption(e.value)}
                                    checked={refundOption === 'currency-you-pay'}
                                />
                                <div className="flex-1">
                                    <label htmlFor="currency-you-pay" className="font-bold text-900 cursor-pointer">
                                        Refund in the currency you pay
                                    </label>
                                    <p className="text-500 mt-2 mb-0">
                                        Cancelled transfers will be refunded in the currency you pay. FX cancellation
                                        gain or loss may be applicable.*
                                    </p>
                                </div>
                            </div>
                        </Card>
                    </div>
                    <div className="col-12 md:col-6">
                        <Card className="h-full bg-indigo-50 border-indigo-200">
                            <div className="flex align-items-start gap-3">
                                <RadioButton
                                    inputId="currency-recipient-gets"
                                    name="refund"
                                    value="currency-recipient-gets"
                                    onChange={(e) => setRefundOption(e.value)}
                                    checked={refundOption === 'currency-recipient-gets'}
                                />
                                <div className="flex-1">
                                    <div className="flex align-items-center gap-2 mb-2">
                                        <label htmlFor="currency-recipient-gets" className="font-bold text-900 cursor-pointer">
                                            Refund in the currency recipient gets
                                        </label>
                                        <i className="pi pi-info-circle text-indigo-500"></i>
                                    </div>
                                    <p className="text-500 mb-3">
                                        Cancelled transfers will be refunded in the currency recipient gets. For{' '}
                                        <span className="font-bold">currencies that cannot be held in the Wallet</span>,
                                        refunds are issued in the currency you pay. FX cancellation gain or loss may be applicable.*
                                    </p>
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>

                <p className="text-xs text-500 mt-4">
                    *FX cancellation gain or loss is applicable in the event that the market rate has moved from
                    the rate of the original conversion.
                </p>
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50">
            <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                <TabPanel header="Summary">
                    {renderSummaryTab()}
                </TabPanel>
                <TabPanel header="Transfer approvals">
                    {renderTransferApprovalsTab()}
                </TabPanel>
                <TabPanel header="Settings">
                    {renderSettingsTab()}
                </TabPanel>
            </TabView>
        </div>
    );
};

export default TransfersPage;
