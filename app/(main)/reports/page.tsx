'use client';
import React from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';

const ReportsPage = () => {
    return (
        <div className="grid">
            <div className="col-12">
                <Card title="Reports" className="mb-4">
                    <div className="flex align-items-center justify-content-center" style={{ minHeight: '300px' }}>
                        <div className="text-center">
                            <i className="pi pi-chart-bar text-6xl text-500 mb-3"></i>
                            <h3 className="text-500">Financial Reports</h3>
                            <p className="text-500 mb-4">Generate and view financial reports</p>
                            <Button label="Generate Report" icon="pi pi-file-pdf" className="p-button-primary" />
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default ReportsPage;
