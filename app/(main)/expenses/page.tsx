'use client';
import React from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';

const ExpensesPage = () => {
    return (
        <div className="grid">
            <div className="col-12">
                <Card title="Expenses" className="mb-4">
                    <div className="flex align-items-center justify-content-center" style={{ minHeight: '300px' }}>
                        <div className="text-center">
                            <i className="pi pi-dollar text-6xl text-500 mb-3"></i>
                            <h3 className="text-500">Expenses Management</h3>
                            <p className="text-500 mb-4">Track and manage your business expenses</p>
                            <Button label="Add Expense" icon="pi pi-plus" className="p-button-primary" />
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default ExpensesPage;
